<div style={{width: '100%', height: '100%', position: 'relative', background: 'var(--color-neutral-100, white)', overflow: 'hidden', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
    <div style={{width: 1512, paddingRight: 16, background: 'var(--bg_primary, #FAFAFA)', borderBottom: '1px var(--line, #C8C8C8) solid', justifyContent: 'space-between', alignItems: 'flex-start', display: 'inline-flex', flexWrap: 'wrap', alignContent: 'flex-start'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-end', alignItems: 'center', gap: 20, display: 'flex'}}>
            <div style={{flex: '1 1 0', paddingLeft: 16, paddingRight: 16, paddingTop: 5, paddingBottom: 5, background: 'linear-gradient(359deg, #0B4D4B 0%, #0B4D4B 63%, #13D4A4 97%)', borderBottomRightRadius: 32, justifyContent: 'flex-start', alignItems: 'center', gap: 21, display: 'flex'}}>
                <div style={{width: 67.15, height: 10.88, background: '#FFFF00'}} />
                <div style={{width: 192.71, height: 12.01, background: 'white'}} />
                <div style={{paddingLeft: 12, paddingRight: 12, paddingTop: 3, paddingBottom: 3, background: '#08F2BA', borderRadius: 12, justifyContent: 'center', alignItems: 'center', gap: 6, display: 'flex'}}>
                    <div style={{color: '#021725', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>無料診断</div>
                    <div style={{width: 3, height: 6, outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                </div>
            </div>
            <div style={{justifyContent: 'flex-end', alignItems: 'center', gap: 11, display: 'flex'}}>
                <div data-プロパティ1="デフォルト" style={{width: 28, height: 28, padding: 5, background: 'var(--bg_primary, #FAFAFA)', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'flex'}} />
                <div style={{width: 158, height: 16, position: 'relative'}}>
                    <div style={{width: 158, height: 16.04, left: 0, top: -0.02, position: 'absolute', background: '#005BAC'}} />
                </div>
            </div>
        </div>
    </div>
    <div style={{alignSelf: 'stretch', height: 800, paddingTop: 64, paddingBottom: 32, background: 'var(--color-neutral-100, white)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 64, display: 'flex'}}>
        <div style={{width: 1200, flex: '1 1 0', paddingTop: 120, paddingBottom: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'flex'}}>
            <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 64, display: 'flex'}}>
                <div style={{overflow: 'hidden', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                    <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 30, fontFamily: 'A-OTF UD Shin Go Con80 Pr6N', fontWeight: '600', lineHeight: 45, letterSpacing: 1.50, wordWrap: 'break-word'}}>総合ネットセキュリティサービス</div>
                    <div style={{width: 588, height: 53, position: 'relative', overflow: 'hidden'}}>
                        <div style={{width: 252.49, height: 51.77, left: 0, top: 0.94, position: 'absolute', background: '#005BAC'}} />
                        <div style={{width: 247.10, height: 51.49, left: 265.46, top: 1.07, position: 'absolute', background: '#009080'}} />
                        <div style={{width: 67.04, height: 53, left: 520.96, top: 0.01, position: 'absolute', background: '#021725'}} />
                        <div style={{width: 64.69, height: 50.66, left: 522.13, top: 1.17, position: 'absolute', background: '#FFF000'}} />
                    </div>
                </div>
                <div style={{width: 792, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'flex'}}>
                    <div style={{width: 792, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                        <div data-プロパティ1="check" style={{width: 792, height: 182, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 16, display: 'flex'}}>
                            <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>パスワードの漏洩、WEBの侵入リスクなどを無料でお調べいたします</div>
                            <div style={{alignSelf: 'stretch', padding: 16, background: 'white', boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', borderRadius: 40, outline: '1px #C8C8C8 solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                    <div style={{flex: '1 1 0', paddingLeft: 16, paddingRight: 16, paddingTop: 4, paddingBottom: 4, justifyContent: 'space-between', alignItems: 'center', display: 'flex'}}>
                                        <div style={{flex: '1 1 0', color: 'rgba(2, 23, 37, 0.38)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>メールアドレス or URLを入力</div>
                                    </div>
                                    <div data-プロパティ1="デフォルト" style={{paddingLeft: 20, paddingRight: 20, paddingTop: 14, paddingBottom: 14, background: '#06F2B9', boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.10)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
                                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
                                            <div style={{width: 14, height: 14, outline: '2px #021725 solid', outlineOffset: '-1px'}} />
                                            <div style={{textAlign: 'center', color: '#021725', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>無料診断</div>
                                        </div>
                                    </div>
                                </div>
                                <div data-プロパティ1="デフォルト" style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div data-プロパティ1="pressed" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'rgba(8, 242, 186, 0.20)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                                        <div style={{width: 18.56, height: 18, position: 'relative'}}>
                                            <div style={{width: 8.86, height: 6.07, left: 4.85, top: 11.93, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 8.86, height: 6.07, left: 4.85, top: 11.93, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 18.56, height: 9.84, left: 0, top: 8.16, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 9.50, height: 9.93, left: 4.53, top: 0, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 9.44, height: 2.80, left: 4.56, top: 3.22, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 5.37, height: 5, left: 6.59, top: 3.93, position: 'absolute', outline: '1px #008F79 solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                        <div style={{textAlign: 'center', color: '#008F79', fontSize: 13, fontFamily: 'Inter', fontWeight: '400', lineHeight: 19.50, wordWrap: 'break-word'}}>パスワード漏洩・Webサイトリスク診断</div>
                                    </div>
                                    <div data-プロパティ1="デフォルト" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--color-neutral-100, white)', borderRadius: 40, outline: '1px #C8C8C8 solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                                        <div style={{width: 23, height: 18, position: 'relative', overflow: 'hidden'}}>
                                            <div style={{width: 11.81, height: 11.82, left: 0.80, top: 1.21, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 11.37, height: 11.82, left: 10.92, top: 4.97, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 1.20, height: 1.20, left: 15.85, top: 10.40, position: 'absolute', background: 'var(--color-neutral-800, #5D5D5D)', borderRadius: 9999}} />
                                            <div style={{width: 1.20, height: 1.20, left: 17.95, top: 10.40, position: 'absolute', background: 'var(--color-neutral-800, #5D5D5D)', borderRadius: 9999}} />
                                            <div style={{width: 1.20, height: 1.20, left: 13.75, top: 10.40, position: 'absolute', background: 'var(--color-neutral-800, #5D5D5D)', borderRadius: 9999}} />
                                            <div style={{width: 6, height: 7, left: 3.80, top: 3.80, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'flex'}}>
                                            <div style={{textAlign: 'center', color: 'var(--color-neutral-800, #5D5D5D)', fontSize: 13, fontFamily: 'Inter', fontWeight: '400', lineHeight: 19.50, wordWrap: 'break-word'}}>その他のセキュリティ相談</div>
                                        </div>
                                    </div>
                                    <div data-プロパティ1="デフォルト" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--color-neutral-100, white)', borderRadius: 40, outline: '1px #C8C8C8 solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                                        <div style={{width: 17, height: 18, position: 'relative', overflow: 'hidden'}}>
                                            <div style={{width: 16.05, height: 10.79, left: 0.47, top: 0.47, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 7, height: 10, left: 5, top: 0.47, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 12.56, height: 1.18, left: 2.22, top: 3.56, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 3.11, height: 2.19, left: 2.75, top: 13.59, position: 'absolute', outline: '0.80px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.40px'}} />
                                            <div style={{width: 3.11, height: 2.19, left: 6.94, top: 13.59, position: 'absolute', outline: '0.80px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.40px'}} />
                                            <div style={{width: 3.11, height: 2.19, left: 11.13, top: 13.59, position: 'absolute', outline: '0.80px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.40px'}} />
                                            <div style={{width: 15.97, height: 5.68, left: 0.51, top: 11.84, position: 'absolute', outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'flex'}}>
                                            <div style={{textAlign: 'center', color: 'var(--color-neutral-800, #5D5D5D)', fontSize: 13, fontFamily: 'Inter', fontWeight: '400', lineHeight: 19.50, wordWrap: 'break-word'}}>「.貴社名」取得はこちら</div>
                                            <div style={{width: 5, height: 8, outline: '1px var(--color-neutral-800, #5D5D5D) solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-プロパティ1="デフォルト" style={{width: 486, paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, background: 'var(--color-neutral-100, white)', borderRadius: 40, outline: '2px #08F2BA solid', outlineOffset: '-2px', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{width: 438, justifyContent: 'space-between', alignItems: 'center', display: 'flex'}}>
                            <div style={{textAlign: 'center'}}><span style="color: '#008F79', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'">10年に1度の取得チャンス！「</span><span style="color: '#008F79', fontSize: 16, fontFamily: 'Inter', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'">.貴社名</span><span style="color: '#008F79', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'">」でなりすまし対策</span></div>
                            <div style={{width: 8, height: 12, outline: '1px var(--color-securityGreen-600, #009080) solid', outlineOffset: '-0.50px'}} />
                        </div>
                    </div>
                </div>
            </div>
            <div style={{textAlign: 'center'}}><span style="color: 'var(--Gray, #97A3A1)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'">※ </span><span style="color: 'var(--Gray, #97A3A1)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', textDecoration: 'underline', lineHeight: 18, wordWrap: 'break-word'">利用規約</span><span style="color: 'var(--Gray, #97A3A1)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'"> と </span><span style="color: 'var(--Gray, #97A3A1)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', textDecoration: 'underline', lineHeight: 18, wordWrap: 'break-word'">プライバシーポリシー</span><span style="color: 'var(--Gray, #97A3A1)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'"> をお読みいただき、同意の上で開始してください。</span></div>
        </div>
    </div>
    <div style={{alignSelf: 'stretch', paddingTop: 64, paddingBottom: 64, background: 'var(--color-neutral-50, #F2F3F4)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
        <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 30, display: 'flex'}}>
            <div style={{width: 996, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 60, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 40, display: 'inline-flex', flexWrap: 'wrap', alignContent: 'flex-start'}}>
                    <img style={{width: 52, height: 42}} src="https://placehold.co/52x42" />
                    <div style={{width: 65, height: 42, background: 'white'}} />
                    <img style={{width: 71, height: 71}} src="https://placehold.co/71x71" />
                    <div style={{width: 111, height: 42, background: 'white'}} />
                    <img style={{width: 123.50, height: 42}} src="https://placehold.co/123x42" />
                    <img style={{width: 124, height: 42}} src="https://placehold.co/124x42" />
                    <div style={{width: 76, height: 42, background: 'white'}} />
                    <img style={{width: 94, height: 31.97}} src="https://placehold.co/94x32" />
                    <div style={{width: 120, height: 42, background: 'white'}} />
                    <img style={{width: 136.73, height: 46.50}} src="https://placehold.co/137x46" />
                    <div style={{width: 90, height: 42, background: 'white'}} />
                    <img style={{width: 104.39, height: 35.50}} src="https://placehold.co/104x35" />
                    <div style={{width: 149, height: 42, background: 'white'}} />
                    <img style={{width: 170.50, height: 57.98}} src="https://placehold.co/170x58" />
                    <img style={{width: 80, height: 42}} src="https://placehold.co/80x42" />
                    <div style={{width: 94, height: 42, background: 'white'}} />
                    <img style={{width: 105, height: 42}} src="https://placehold.co/105x42" />
                    <img style={{width: 119, height: 42}} src="https://placehold.co/119x42" />
                    <div style={{width: 118, height: 42, background: 'white'}} />
                    <img style={{width: 134.60, height: 89.62}} src="https://placehold.co/135x90" />
                    <div style={{width: 96, height: 42, background: 'white'}} />
                    <img style={{width: 99.88, height: 66.50}} src="https://placehold.co/100x66" />
                    <div style={{width: 73, height: 42, background: 'white'}} />
                    <img style={{width: 88.50, height: 58.89}} src="https://placehold.co/88x59" />
                    <div style={{width: 121, height: 42, background: 'white'}} />
                    <img style={{width: 128, height: 22.94}} src="https://placehold.co/128x23" />
                    <div style={{width: 80, height: 42, background: 'white'}} />
                    <img style={{width: 84.21, height: 32}} src="https://placehold.co/84x32" />
                    <div style={{width: 123, height: 42, background: 'white'}} />
                    <img style={{width: 136.50, height: 136.50}} src="https://placehold.co/136x136" />
                    <div style={{width: 105, height: 42, background: 'white'}} />
                    <img style={{width: 113.50, height: 28.38}} src="https://placehold.co/113x28" />
                    <div style={{width: 45, height: 42, background: 'white'}} />
                    <img style={{width: 45, height: 45}} src="https://placehold.co/45x45" />
                </div>
                <div style={{justifyContent: 'center', alignItems: 'center', gap: 20, display: 'inline-flex'}}>
                    <img style={{width: 384, height: 217}} src="https://placehold.co/384x217" />
                </div>
            </div>
        </div>
    </div>
    <div style={{width: 1512, paddingLeft: 156, paddingRight: 156, paddingTop: 96, paddingBottom: 96, background: 'white', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 64, display: 'flex'}}>
        <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
            <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 48, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 72, wordWrap: 'break-word'}}>セキュリティ事業</div>
            <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>GMOインターネットグループのセキュリティ事業4社で、安心・安全なインターネットをご提供致します。</div>
            <div style={{alignSelf: 'stretch', height: 224, boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 2, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                    <div style={{flex: '1 1 0', paddingTop: 16, paddingBottom: 16, background: 'var(--color-securityGreen-600, #009080)', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>実在証明・盗聴対策</div>
                            <div style={{justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                <div style={{width: 7, textAlign: 'right', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>（</div>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>暗号セキュリティ</div>
                                <div style={{width: 4, color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>）</div>
                            </div>
                        </div>
                    </div>
                    <div style={{width: 588, paddingTop: 16, paddingBottom: 16, background: 'var(--color-securityGreen-600, #009080)', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>サイバー攻撃対策</div>
                            <div style={{justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                <div style={{width: 7, textAlign: 'right', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>（</div>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>暗号セキュリティ</div>
                                <div style={{width: 4, color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>）</div>
                            </div>
                        </div>
                    </div>
                    <div style={{flex: '1 1 0', paddingTop: 16, paddingBottom: 16, background: 'var(--color-securityGreen-600, #009080)', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>なりすまし対策</div>
                            <div style={{justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                <div style={{width: 7, textAlign: 'right', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>（</div>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>暗号セキュリティ</div>
                                <div style={{width: 4, color: 'var(--color-neutral-100, white)', fontSize: 12, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '300', lineHeight: 20, wordWrap: 'break-word'}}>）</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style={{alignSelf: 'stretch', flex: '1 1 0', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                    <div style={{flex: '1 1 0', padding: 20, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'flex'}}>
                            <div style={{width: 214.48, height: 46, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 214.48, height: 46, left: 0, top: 0, position: 'absolute', background: 'black'}} />
                                <div style={{width: 5.48, height: 8.17, left: 93.62, top: 36.05, position: 'absolute', background: '#004690'}} />
                                <div style={{width: 5.19, height: 7.61, left: 99.34, top: 38.39, position: 'absolute', background: '#004690'}} />
                                <div style={{width: 19.63, height: 11.95, left: 126.94, top: 32.45, position: 'absolute', background: '#004690'}} />
                                <div style={{width: 20.42, height: 12.26, left: 106.75, top: 32.30, position: 'absolute', background: '#004690'}} />
                                <div style={{width: 20.42, height: 12.26, left: 146.33, top: 32.29, position: 'absolute', background: '#004690'}} />
                                <div style={{width: 45.63, height: 45.63, left: 0, top: 0, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 45.44, height: 45.44, left: 0.09, top: 0.09, position: 'absolute', background: '#004993'}} />
                                <div style={{width: 45.25, height: 45.25, left: 0.19, top: 0.19, position: 'absolute', background: '#004A93'}} />
                                <div style={{width: 45.06, height: 45.06, left: 0.28, top: 0.28, position: 'absolute', background: '#004B94'}} />
                                <div style={{width: 44.88, height: 44.88, left: 0.38, top: 0.38, position: 'absolute', background: '#004B94'}} />
                                <div style={{width: 44.69, height: 44.69, left: 0.47, top: 0.47, position: 'absolute', background: '#004B94'}} />
                                <div style={{width: 44.50, height: 44.50, left: 0.56, top: 0.56, position: 'absolute', background: '#004B95'}} />
                                <div style={{width: 44.31, height: 44.31, left: 0.66, top: 0.66, position: 'absolute', background: '#004C95'}} />
                                <div style={{width: 44.13, height: 44.13, left: 0.75, top: 0.75, position: 'absolute', background: '#004C95'}} />
                                <div style={{width: 43.94, height: 43.94, left: 0.84, top: 0.84, position: 'absolute', background: '#004D96'}} />
                                <div style={{width: 43.75, height: 43.75, left: 0.93, top: 0.94, position: 'absolute', background: '#004E96'}} />
                                <div style={{width: 43.56, height: 43.56, left: 1.03, top: 1.03, position: 'absolute', background: '#004E97'}} />
                                <div style={{width: 43.38, height: 43.38, left: 1.12, top: 1.12, position: 'absolute', background: '#004F97'}} />
                                <div style={{width: 43.19, height: 43.19, left: 1.22, top: 1.21, position: 'absolute', background: '#004F98'}} />
                                <div style={{width: 43, height: 43, left: 1.31, top: 1.32, position: 'absolute', background: '#005099'}} />
                                <div style={{width: 42.81, height: 42.81, left: 1.41, top: 1.41, position: 'absolute', background: '#00519A'}} />
                                <div style={{width: 42.63, height: 42.63, left: 1.50, top: 1.50, position: 'absolute', background: '#00519A'}} />
                                <div style={{width: 42.44, height: 42.44, left: 1.59, top: 1.59, position: 'absolute', background: '#00519B'}} />
                                <div style={{width: 42.25, height: 42.25, left: 1.69, top: 1.69, position: 'absolute', background: '#00519B'}} />
                                <div style={{width: 42.07, height: 42.07, left: 1.78, top: 1.78, position: 'absolute', background: '#00539C'}} />
                                <div style={{width: 41.88, height: 41.88, left: 1.87, top: 1.87, position: 'absolute', background: '#00539C'}} />
                                <div style={{width: 41.69, height: 41.69, left: 1.97, top: 1.97, position: 'absolute', background: '#00549C'}} />
                                <div style={{width: 41.50, height: 41.50, left: 2.06, top: 2.06, position: 'absolute', background: '#00549D'}} />
                                <div style={{width: 41.32, height: 41.32, left: 2.16, top: 2.16, position: 'absolute', background: '#00549D'}} />
                                <div style={{width: 41.13, height: 41.13, left: 2.25, top: 2.25, position: 'absolute', background: '#00559E'}} />
                                <div style={{width: 40.94, height: 40.94, left: 2.34, top: 2.34, position: 'absolute', background: '#00559E'}} />
                                <div style={{width: 40.75, height: 40.75, left: 2.44, top: 2.44, position: 'absolute', background: '#00569F'}} />
                                <div style={{width: 40.57, height: 40.57, left: 2.53, top: 2.54, position: 'absolute', background: '#00579F'}} />
                                <div style={{width: 40.38, height: 40.38, left: 2.62, top: 2.62, position: 'absolute', background: '#0057A0'}} />
                                <div style={{width: 40.20, height: 40.20, left: 2.71, top: 2.72, position: 'absolute', background: '#0058A1'}} />
                                <div style={{width: 40, height: 40, left: 2.81, top: 2.82, position: 'absolute', background: '#0058A1'}} />
                                <div style={{width: 39.82, height: 39.82, left: 2.90, top: 2.90, position: 'absolute', background: '#0059A2'}} />
                                <div style={{width: 39.63, height: 39.63, left: 3, top: 3, position: 'absolute', background: '#0059A2'}} />
                                <div style={{width: 39.44, height: 39.44, left: 3.09, top: 3.09, position: 'absolute', background: '#005AA3'}} />
                                <div style={{width: 39.25, height: 39.25, left: 3.19, top: 3.19, position: 'absolute', background: '#005AA2'}} />
                                <div style={{width: 39.07, height: 39.07, left: 3.27, top: 3.28, position: 'absolute', background: '#005AA3'}} />
                                <div style={{width: 38.88, height: 38.88, left: 3.37, top: 3.37, position: 'absolute', background: '#005BA4'}} />
                                <div style={{width: 38.69, height: 38.69, left: 3.47, top: 3.47, position: 'absolute', background: '#005BA4'}} />
                                <div style={{width: 38.51, height: 38.51, left: 3.56, top: 3.56, position: 'absolute', background: '#005CA4'}} />
                                <div style={{width: 38.32, height: 38.32, left: 3.65, top: 3.66, position: 'absolute', background: '#005DA5'}} />
                                <div style={{width: 38.13, height: 38.13, left: 3.75, top: 3.75, position: 'absolute', background: '#005DA6'}} />
                                <div style={{width: 37.94, height: 37.94, left: 3.84, top: 3.84, position: 'absolute', background: '#005EA6'}} />
                                <div style={{width: 37.76, height: 37.76, left: 3.93, top: 3.94, position: 'absolute', background: '#005EA7'}} />
                                <div style={{width: 37.57, height: 37.57, left: 4.03, top: 4.03, position: 'absolute', background: '#005FA7'}} />
                                <div style={{width: 37.38, height: 37.38, left: 4.12, top: 4.12, position: 'absolute', background: '#0060A8'}} />
                                <div style={{width: 37.20, height: 37.20, left: 4.21, top: 4.22, position: 'absolute', background: '#0060A8'}} />
                                <div style={{width: 37, height: 37, left: 4.32, top: 4.32, position: 'absolute', background: '#0061A9'}} />
                                <div style={{width: 36.82, height: 36.82, left: 4.40, top: 4.40, position: 'absolute', background: '#0061A9'}} />
                                <div style={{width: 36.63, height: 36.63, left: 4.50, top: 4.50, position: 'absolute', background: '#0061A9'}} />
                                <div style={{width: 36.44, height: 36.44, left: 4.59, top: 4.59, position: 'absolute', background: '#0062AA'}} />
                                <div style={{width: 36.26, height: 36.26, left: 4.69, top: 4.68, position: 'absolute', background: '#0062AA'}} />
                                <div style={{width: 36.07, height: 36.07, left: 4.78, top: 4.78, position: 'absolute', background: '#0063AB'}} />
                                <div style={{width: 35.88, height: 35.88, left: 4.87, top: 4.87, position: 'absolute', background: '#0065AB'}} />
                                <div style={{width: 35.70, height: 35.70, left: 4.96, top: 4.97, position: 'absolute', background: '#0065AC'}} />
                                <div style={{width: 35.51, height: 35.51, left: 5.06, top: 5.06, position: 'absolute', background: '#0065AC'}} />
                                <div style={{width: 35.32, height: 35.32, left: 5.15, top: 5.15, position: 'absolute', background: '#0065AD'}} />
                                <div style={{width: 35.13, height: 35.13, left: 5.25, top: 5.25, position: 'absolute', background: '#0066AD'}} />
                                <div style={{width: 34.95, height: 34.95, left: 5.34, top: 5.34, position: 'absolute', background: '#0067AD'}} />
                                <div style={{width: 34.76, height: 34.76, left: 5.43, top: 5.43, position: 'absolute', background: '#0067AE'}} />
                                <div style={{width: 34.57, height: 34.57, left: 5.53, top: 5.53, position: 'absolute', background: '#0068AE'}} />
                                <div style={{width: 34.38, height: 34.38, left: 5.62, top: 5.62, position: 'absolute', background: '#0068AF'}} />
                                <div style={{width: 34.20, height: 34.20, left: 5.71, top: 5.71, position: 'absolute', background: '#0069AF'}} />
                                <div style={{width: 34, height: 34, left: 5.82, top: 5.81, position: 'absolute', background: '#006AB0'}} />
                                <div style={{width: 33.82, height: 33.82, left: 5.90, top: 5.90, position: 'absolute', background: '#006AB0'}} />
                                <div style={{width: 33.63, height: 33.63, left: 6, top: 6, position: 'absolute', background: '#006BB1'}} />
                                <div style={{width: 33.45, height: 33.45, left: 6.09, top: 6.09, position: 'absolute', background: '#006BB1'}} />
                                <div style={{width: 33.26, height: 33.26, left: 6.18, top: 6.18, position: 'absolute', background: '#006CB1'}} />
                                <div style={{width: 33.07, height: 33.07, left: 6.28, top: 6.28, position: 'absolute', background: '#006CB2'}} />
                                <div style={{width: 32.88, height: 32.88, left: 6.37, top: 6.37, position: 'absolute', background: '#006DB2'}} />
                                <div style={{width: 32.70, height: 32.70, left: 6.46, top: 6.46, position: 'absolute', background: '#006EB3'}} />
                                <div style={{width: 32.51, height: 32.51, left: 6.56, top: 6.56, position: 'absolute', background: '#006EB3'}} />
                                <div style={{width: 32.32, height: 32.32, left: 6.65, top: 6.65, position: 'absolute', background: '#006FB4'}} />
                                <div style={{width: 32.13, height: 32.13, left: 6.75, top: 6.75, position: 'absolute', background: '#0070B4'}} />
                                <div style={{width: 31.95, height: 31.95, left: 6.84, top: 6.84, position: 'absolute', background: '#0070B5'}} />
                                <div style={{width: 31.76, height: 31.76, left: 6.93, top: 6.94, position: 'absolute', background: '#0071B5'}} />
                                <div style={{width: 31.57, height: 31.57, left: 7.03, top: 7.03, position: 'absolute', background: '#0071B7'}} />
                                <div style={{width: 31.38, height: 31.38, left: 7.12, top: 7.12, position: 'absolute', background: '#0071B6'}} />
                                <div style={{width: 31.20, height: 31.20, left: 7.21, top: 7.21, position: 'absolute', background: '#0073B6'}} />
                                <div style={{width: 31.01, height: 31.01, left: 7.31, top: 7.31, position: 'absolute', background: '#0073B8'}} />
                                <div style={{width: 30.82, height: 30.82, left: 7.41, top: 7.40, position: 'absolute', background: '#0074B7'}} />
                                <div style={{width: 30.63, height: 30.63, left: 7.50, top: 7.50, position: 'absolute', background: '#0074B8'}} />
                                <div style={{width: 30.45, height: 30.45, left: 7.59, top: 7.59, position: 'absolute', background: '#0074B8'}} />
                                <div style={{width: 30.26, height: 30.26, left: 7.68, top: 7.68, position: 'absolute', background: '#0075B9'}} />
                                <div style={{width: 30.07, height: 30.07, left: 7.78, top: 7.78, position: 'absolute', background: '#0075B9'}} />
                                <div style={{width: 29.89, height: 29.89, left: 7.87, top: 7.87, position: 'absolute', background: '#0076B8'}} />
                                <div style={{width: 29.70, height: 29.70, left: 7.96, top: 7.96, position: 'absolute', background: '#0077BA'}} />
                                <div style={{width: 29.51, height: 29.51, left: 8.06, top: 8.06, position: 'absolute', background: '#0077BA'}} />
                                <div style={{width: 29.32, height: 29.32, left: 8.15, top: 8.15, position: 'absolute', background: '#0078BB'}} />
                                <div style={{width: 29.14, height: 29.14, left: 8.24, top: 8.25, position: 'absolute', background: '#0078BB'}} />
                                <div style={{width: 28.95, height: 28.95, left: 8.34, top: 8.34, position: 'absolute', background: '#0079BC'}} />
                                <div style={{width: 28.76, height: 28.76, left: 8.43, top: 8.43, position: 'absolute', background: '#007ABB'}} />
                                <div style={{width: 28.57, height: 28.57, left: 8.53, top: 8.53, position: 'absolute', background: '#007ABB'}} />
                                <div style={{width: 28.39, height: 28.39, left: 8.62, top: 8.63, position: 'absolute', background: '#007BBD'}} />
                                <div style={{width: 28.20, height: 28.20, left: 8.71, top: 8.72, position: 'absolute', background: '#007BBD'}} />
                                <div style={{width: 28.01, height: 28.01, left: 8.81, top: 8.81, position: 'absolute', background: '#007CBD'}} />
                                <div style={{width: 27.82, height: 27.82, left: 8.90, top: 8.90, position: 'absolute', background: '#007DBE'}} />
                                <div style={{width: 27.64, height: 27.64, left: 8.99, top: 9, position: 'absolute', background: '#007DBF'}} />
                                <div style={{width: 27.45, height: 27.45, left: 9.09, top: 9.09, position: 'absolute', background: '#007EBE'}} />
                                <div style={{width: 27.26, height: 27.26, left: 9.18, top: 9.18, position: 'absolute', background: '#007FC0'}} />
                                <div style={{width: 27.08, height: 27.08, left: 9.27, top: 9.28, position: 'absolute', background: '#007FC0'}} />
                                <div style={{width: 26.89, height: 26.89, left: 9.37, top: 9.37, position: 'absolute', background: '#0080C0'}} />
                                <div style={{width: 26.70, height: 26.70, left: 9.46, top: 9.46, position: 'absolute', background: '#0081C1'}} />
                                <div style={{width: 26.51, height: 26.51, left: 9.55, top: 9.56, position: 'absolute', background: '#0081C1'}} />
                                <div style={{width: 26.33, height: 26.33, left: 9.65, top: 9.65, position: 'absolute', background: '#0081C1'}} />
                                <div style={{width: 26.14, height: 26.14, left: 9.74, top: 9.75, position: 'absolute', background: '#0081C1'}} />
                                <div style={{width: 25.95, height: 25.95, left: 9.84, top: 9.84, position: 'absolute', background: '#0083C3'}} />
                                <div style={{width: 25.76, height: 25.76, left: 9.93, top: 9.93, position: 'absolute', background: '#0083C3'}} />
                                <div style={{width: 25.58, height: 25.58, left: 10.03, top: 10.03, position: 'absolute', background: '#0083C3'}} />
                                <div style={{width: 25.39, height: 25.39, left: 10.12, top: 10.12, position: 'absolute', background: '#0085C4'}} />
                                <div style={{width: 25.20, height: 25.20, left: 10.21, top: 10.21, position: 'absolute', background: '#0085C4'}} />
                                <div style={{width: 25.01, height: 25.01, left: 10.30, top: 10.31, position: 'absolute', background: '#0085C5'}} />
                                <div style={{width: 24.83, height: 24.83, left: 10.40, top: 10.40, position: 'absolute', background: '#0087C5'}} />
                                <div style={{width: 24.64, height: 24.64, left: 10.49, top: 10.49, position: 'absolute', background: '#0087C7'}} />
                                <div style={{width: 24.45, height: 24.45, left: 10.59, top: 10.59, position: 'absolute', background: '#0088C7'}} />
                                <div style={{width: 24.26, height: 24.26, left: 10.68, top: 10.68, position: 'absolute', background: '#0089C6'}} />
                                <div style={{width: 24.08, height: 24.08, left: 10.78, top: 10.78, position: 'absolute', background: '#0089C7'}} />
                                <div style={{width: 23.89, height: 23.89, left: 10.87, top: 10.87, position: 'absolute', background: '#008AC7'}} />
                                <div style={{width: 23.70, height: 23.70, left: 10.96, top: 10.96, position: 'absolute', background: '#008AC8'}} />
                                <div style={{width: 23.52, height: 23.52, left: 11.05, top: 11.06, position: 'absolute', background: '#008BC8'}} />
                                <div style={{width: 23.33, height: 23.33, left: 11.15, top: 11.15, position: 'absolute', background: '#008CCA'}} />
                                <div style={{width: 23.14, height: 23.14, left: 11.24, top: 11.24, position: 'absolute', background: '#008CCA'}} />
                                <div style={{width: 22.95, height: 22.95, left: 11.33, top: 11.34, position: 'absolute', background: '#008DCA'}} />
                                <div style={{width: 22.77, height: 22.77, left: 11.43, top: 11.43, position: 'absolute', background: '#008ECA'}} />
                                <div style={{width: 22.58, height: 22.58, left: 11.52, top: 11.53, position: 'absolute', background: '#008ECA'}} />
                                <div style={{width: 22.40, height: 22.40, left: 11.61, top: 11.62, position: 'absolute', background: '#008FCC'}} />
                                <div style={{width: 22.20, height: 22.20, left: 11.71, top: 11.71, position: 'absolute', background: '#0091CC'}} />
                                <div style={{width: 22.02, height: 22.02, left: 11.80, top: 11.81, position: 'absolute', background: '#0091CD'}} />
                                <div style={{width: 21.83, height: 21.83, left: 11.90, top: 11.90, position: 'absolute', background: '#0091CD'}} />
                                <div style={{width: 21.64, height: 21.64, left: 11.99, top: 11.99, position: 'absolute', background: '#0091CD'}} />
                                <div style={{width: 21.45, height: 21.45, left: 12.09, top: 12.09, position: 'absolute', background: '#0092CE'}} />
                                <div style={{width: 21.27, height: 21.27, left: 12.18, top: 12.18, position: 'absolute', background: '#0093CE'}} />
                                <div style={{width: 21.08, height: 21.08, left: 12.27, top: 12.28, position: 'absolute', background: '#0093CF'}} />
                                <div style={{width: 20.89, height: 20.89, left: 12.37, top: 12.37, position: 'absolute', background: '#0095CF'}} />
                                <div style={{width: 20.71, height: 20.71, left: 12.46, top: 12.46, position: 'absolute', background: '#0095D0'}} />
                                <div style={{width: 20.52, height: 20.52, left: 12.55, top: 12.56, position: 'absolute', background: '#0095D1'}} />
                                <div style={{width: 20.33, height: 20.33, left: 12.65, top: 12.65, position: 'absolute', background: '#0097D1'}} />
                                <div style={{width: 20.14, height: 20.14, left: 12.74, top: 12.74, position: 'absolute', background: '#0098D2'}} />
                                <div style={{width: 19.95, height: 19.95, left: 12.84, top: 12.84, position: 'absolute', background: '#0098D2'}} />
                                <div style={{width: 19.77, height: 19.77, left: 12.93, top: 12.93, position: 'absolute', background: '#0099D3'}} />
                                <div style={{width: 19.58, height: 19.58, left: 13.02, top: 13.03, position: 'absolute', background: '#0099D3'}} />
                                <div style={{width: 19.40, height: 19.40, left: 13.11, top: 13.12, position: 'absolute', background: '#009AD3'}} />
                                <div style={{width: 19.20, height: 19.20, left: 13.22, top: 13.22, position: 'absolute', background: '#009BD5'}} />
                                <div style={{width: 19.02, height: 19.02, left: 13.30, top: 13.30, position: 'absolute', background: '#009BD5'}} />
                                <div style={{width: 18.83, height: 18.83, left: 13.40, top: 13.40, position: 'absolute', background: '#00A9EA'}} />
                                <div style={{width: 23.58, height: 23.58, left: 11.03, top: 11.03, position: 'absolute', background: '#090406'}} />
                                <div style={{width: 10.38, height: 10.37, left: 9.78, top: 6.44, position: 'absolute', background: 'white'}} />
                                <div style={{width: 19.28, height: 20.20, left: 55.77, top: 5.58, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 3.78, height: 21.01, left: 78.83, top: 4.56, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 15.91, height: 14.99, left: 85.79, top: 10.94, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 15.65, height: 21.37, left: 104.78, top: 4.56, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 13.18, height: 15, left: 122.53, top: 10.93, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 3.78, height: 21.01, left: 139.61, top: 4.56, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 14.64, height: 20.49, left: 146.85, top: 5.44, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 4.49, height: 20.99, left: 164.52, top: 4.59, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 15.20, height: 20.89, left: 171.85, top: 10.92, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 14.07, height: 14.65, left: 191.05, top: 10.94, position: 'absolute', background: '#004DA0'}} />
                                <div style={{width: 5.48, height: 5.48, left: 209.08, top: 20.07, position: 'absolute', background: '#090406'}} />
                                <div style={{width: 2.75, height: 3.13, left: 210.58, top: 21.20, position: 'absolute', background: '#090406'}} />
                            </div>
                            <div style={{width: 24, height: 8, outline: '2px #005BAC solid', outlineOffset: '-1px'}} />
                        </div>
                    </div>
                    <div style={{width: 588, alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 2, display: 'flex'}}>
                        <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 20, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'flex'}}>
                                <div style={{width: 252, height: 38, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 251.10, height: 37.81, left: 0.45, top: 0.10, position: 'absolute', background: 'black'}} />
                                    <div style={{width: 17.90, height: 14.75, left: 83.92, top: 23.15, position: 'absolute', background: '#009FA5'}} />
                                    <div style={{width: 49.31, height: 12.59, left: 108.21, top: 24.19, position: 'absolute', background: '#021725'}} />
                                    <div style={{width: 166.17, height: 16.30, left: 85.39, top: 0.29, position: 'absolute', background: '#5A5858'}} />
                                    <div style={{width: 81.28, height: 16.76, left: 0.45, top: 0.10, position: 'absolute', background: '#005BAC'}} />
                                </div>
                                <div style={{width: 24, height: 8, outline: '2px #005BAC solid', outlineOffset: '-1px'}} />
                            </div>
                        </div>
                        <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 20, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'flex'}}>
                                <div style={{width: 252, height: 27, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 27.82, height: 16.83, left: 28.70, top: 4.75, position: 'absolute', background: '#005BAC'}} />
                                    <div style={{width: 28.96, height: 17.29, left: 0.07, top: 4.52, position: 'absolute', background: '#005BAC'}} />
                                    <div style={{width: 28.96, height: 17.28, left: 56.20, top: 4.52, position: 'absolute', background: '#005BAC'}} />
                                    <div style={{width: 18.16, height: 14.58, left: 130.95, top: 7.23, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 15.82, height: 16.67, left: 152.97, top: 5.14, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 12.90, height: 12.19, left: 182.99, top: 9.62, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 11.01, height: 11.94, left: 210.36, top: 9.61, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 7.63, height: 16.54, left: 220.21, top: 5.01, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 23.93, height: 19.71, left: 228, top: 7.23, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 14.67, height: 11.94, left: 196.22, top: 9.88, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 21.47, height: 16.17, left: 89.08, top: 5.39, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 14.43, height: 12.19, left: 115.19, top: 9.62, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 13.73, height: 12.19, left: 168.20, top: 9.62, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 7.74, height: 17.03, left: 108.28, top: 4.52, position: 'absolute', background: '#D22550'}} />
                                    <div style={{width: 251.86, height: 26.95, left: 0.07, top: 0, position: 'absolute'}} />
                                </div>
                                <div style={{width: 24, height: 8, outline: '2px #005BAC solid', outlineOffset: '-1px'}} />
                            </div>
                        </div>
                    </div>
                    <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 20, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'flex'}}>
                            <div style={{width: 252, height: 17, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 10.10, height: 15.67, left: 85.06, top: 0.49, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 11.38, height: 15.65, left: 96.56, top: 0.50, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 14.21, height: 15.94, left: 109.41, top: 0.19, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 13.43, height: 16.22, left: 125.19, top: 0.19, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 14.18, height: 15.65, left: 140.59, top: 0.49, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 9.28, height: 16.24, left: 158.48, top: 0.19, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 8.22, height: 15.65, left: 169.19, top: 0.48, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 13.30, height: 16.24, left: 178.60, top: 0.20, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 13.26, height: 15.95, left: 193.78, top: 0.48, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 11.38, height: 15.65, left: 208.77, top: 0.50, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 2.41, height: 15.65, left: 221.58, top: 0.49, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 11.63, height: 15.66, left: 225.52, top: 0.49, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 12.74, height: 15.66, left: 238.70, top: 0.48, position: 'absolute', background: '#595757'}} />
                                <div style={{width: 26.30, height: 16.27, left: 27.60, top: 0.22, position: 'absolute', background: '#005BAC'}} />
                                <div style={{width: 27.36, height: 16.71, left: 0.54, top: 0, position: 'absolute', background: '#005BAC'}} />
                                <div style={{width: 27.36, height: 16.71, left: 53.58, top: 0, position: 'absolute', background: '#005BAC'}} />
                            </div>
                            <div style={{width: 24, height: 8, outline: '2px #005BAC solid', outlineOffset: '-1px'}} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                <div style={{alignSelf: 'stretch', paddingTop: 60, paddingBottom: 60, background: 'white', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                    <div style={{paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{textAlign: 'center', color: 'white', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                    </div>
                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 45, display: 'flex'}}>
                        <div style={{width: 356, height: 77, position: 'relative', overflow: 'hidden'}}>
                            <div style={{width: 356, height: 76.07, left: 0, top: 0.60, position: 'absolute', background: 'black'}} />
                            <div style={{width: 9.10, height: 13.51, left: 155.39, top: 60.22, position: 'absolute', background: '#004690'}} />
                            <div style={{width: 8.61, height: 12.59, left: 164.89, top: 64.08, position: 'absolute', background: '#004690'}} />
                            <div style={{width: 32.57, height: 19.76, left: 210.69, top: 54.26, position: 'absolute', background: '#004690'}} />
                            <div style={{width: 33.89, height: 20.28, left: 177.19, top: 54, position: 'absolute', background: '#004690'}} />
                            <div style={{width: 33.89, height: 20.28, left: 242.88, top: 54, position: 'absolute', background: '#004690'}} />
                            <div style={{width: 75.73, height: 75.45, left: 0, top: 0.60, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 75.42, height: 75.14, left: 0.15, top: 0.75, position: 'absolute', background: '#004993'}} />
                            <div style={{width: 75.11, height: 74.83, left: 0.31, top: 0.91, position: 'absolute', background: '#004A93'}} />
                            <div style={{width: 74.80, height: 74.52, left: 0.46, top: 1.06, position: 'absolute', background: '#004B94'}} />
                            <div style={{width: 74.49, height: 74.21, left: 0.63, top: 1.22, position: 'absolute', background: '#004B94'}} />
                            <div style={{width: 74.17, height: 73.90, left: 0.78, top: 1.37, position: 'absolute', background: '#004B94'}} />
                            <div style={{width: 73.86, height: 73.59, left: 0.93, top: 1.53, position: 'absolute', background: '#004B95'}} />
                            <div style={{width: 73.55, height: 73.28, left: 1.09, top: 1.68, position: 'absolute', background: '#004C95'}} />
                            <div style={{width: 73.24, height: 72.97, left: 1.24, top: 1.83, position: 'absolute', background: '#004C95'}} />
                            <div style={{width: 72.93, height: 72.66, left: 1.40, top: 1.99, position: 'absolute', background: '#004D96'}} />
                            <div style={{width: 72.62, height: 72.35, left: 1.55, top: 2.15, position: 'absolute', background: '#004E96'}} />
                            <div style={{width: 72.31, height: 72.04, left: 1.71, top: 2.30, position: 'absolute', background: '#004E97'}} />
                            <div style={{width: 72, height: 71.74, left: 1.86, top: 2.46, position: 'absolute', background: '#004F97'}} />
                            <div style={{width: 71.69, height: 71.43, left: 2.02, top: 2.61, position: 'absolute', background: '#004F98'}} />
                            <div style={{width: 71.37, height: 71.11, left: 2.18, top: 2.77, position: 'absolute', background: '#005099'}} />
                            <div style={{width: 71.06, height: 70.80, left: 2.33, top: 2.92, position: 'absolute', background: '#00519A'}} />
                            <div style={{width: 70.76, height: 70.50, left: 2.49, top: 3.07, position: 'absolute', background: '#00519A'}} />
                            <div style={{width: 70.44, height: 70.18, left: 2.64, top: 3.23, position: 'absolute', background: '#00519B'}} />
                            <div style={{width: 70.13, height: 69.87, left: 2.80, top: 3.39, position: 'absolute', background: '#00519B'}} />
                            <div style={{width: 69.82, height: 69.57, left: 2.95, top: 3.54, position: 'absolute', background: '#00539C'}} />
                            <div style={{width: 69.51, height: 69.26, left: 3.11, top: 3.69, position: 'absolute', background: '#00539C'}} />
                            <div style={{width: 69.20, height: 68.95, left: 3.26, top: 3.85, position: 'absolute', background: '#00549C'}} />
                            <div style={{width: 68.89, height: 68.63, left: 3.42, top: 4, position: 'absolute', background: '#00549D'}} />
                            <div style={{width: 68.58, height: 68.33, left: 3.58, top: 4.16, position: 'absolute', background: '#00549D'}} />
                            <div style={{width: 68.27, height: 68.02, left: 3.73, top: 4.31, position: 'absolute', background: '#00559E'}} />
                            <div style={{width: 67.95, height: 67.70, left: 3.89, top: 4.47, position: 'absolute', background: '#00559E'}} />
                            <div style={{width: 67.64, height: 67.39, left: 4.05, top: 4.62, position: 'absolute', background: '#00569F'}} />
                            <div style={{width: 67.33, height: 67.09, left: 4.20, top: 4.79, position: 'absolute', background: '#00579F'}} />
                            <div style={{width: 67.02, height: 66.77, left: 4.36, top: 4.94, position: 'absolute', background: '#0057A0'}} />
                            <div style={{width: 66.73, height: 66.48, left: 4.50, top: 5.09, position: 'absolute', background: '#0058A1'}} />
                            <div style={{width: 66.39, height: 66.15, left: 4.67, top: 5.25, position: 'absolute', background: '#0058A1'}} />
                            <div style={{width: 66.09, height: 65.85, left: 4.82, top: 5.40, position: 'absolute', background: '#0059A2'}} />
                            <div style={{width: 65.78, height: 65.53, left: 4.98, top: 5.55, position: 'absolute', background: '#0059A2'}} />
                            <div style={{width: 65.47, height: 65.23, left: 5.13, top: 5.71, position: 'absolute', background: '#005AA3'}} />
                            <div style={{width: 65.15, height: 64.92, left: 5.29, top: 5.86, position: 'absolute', background: '#005AA2'}} />
                            <div style={{width: 64.85, height: 64.61, left: 5.43, top: 6.02, position: 'absolute', background: '#005AA3'}} />
                            <div style={{width: 64.53, height: 64.30, left: 5.60, top: 6.17, position: 'absolute', background: '#005BA4'}} />
                            <div style={{width: 64.22, height: 63.99, left: 5.75, top: 6.33, position: 'absolute', background: '#005BA4'}} />
                            <div style={{width: 63.91, height: 63.68, left: 5.91, top: 6.48, position: 'absolute', background: '#005CA4'}} />
                            <div style={{width: 63.60, height: 63.37, left: 6.06, top: 6.64, position: 'absolute', background: '#005DA5'}} />
                            <div style={{width: 63.29, height: 63.06, left: 6.22, top: 6.79, position: 'absolute', background: '#005DA6'}} />
                            <div style={{width: 62.98, height: 62.75, left: 6.38, top: 6.95, position: 'absolute', background: '#005EA6'}} />
                            <div style={{width: 62.67, height: 62.44, left: 6.53, top: 7.10, position: 'absolute', background: '#005EA7'}} />
                            <div style={{width: 62.36, height: 62.13, left: 6.69, top: 7.26, position: 'absolute', background: '#005FA7'}} />
                            <div style={{width: 62.04, height: 61.82, left: 6.84, top: 7.41, position: 'absolute', background: '#0060A8'}} />
                            <div style={{width: 61.75, height: 61.52, left: 6.99, top: 7.57, position: 'absolute', background: '#0060A8'}} />
                            <div style={{width: 61.41, height: 61.19, left: 7.16, top: 7.73, position: 'absolute', background: '#0061A9'}} />
                            <div style={{width: 61.11, height: 60.89, left: 7.30, top: 7.87, position: 'absolute', background: '#0061A9'}} />
                            <div style={{width: 60.80, height: 60.58, left: 7.46, top: 8.03, position: 'absolute', background: '#0061A9'}} />
                            <div style={{width: 60.49, height: 60.27, left: 7.62, top: 8.19, position: 'absolute', background: '#0062AA'}} />
                            <div style={{width: 60.18, height: 59.96, left: 7.78, top: 8.34, position: 'absolute', background: '#0062AA'}} />
                            <div style={{width: 59.87, height: 59.65, left: 7.93, top: 8.50, position: 'absolute', background: '#0063AB'}} />
                            <div style={{width: 59.56, height: 59.34, left: 8.09, top: 8.65, position: 'absolute', background: '#0065AB'}} />
                            <div style={{width: 59.25, height: 59.03, left: 8.24, top: 8.81, position: 'absolute', background: '#0065AC'}} />
                            <div style={{width: 58.94, height: 58.72, left: 8.40, top: 8.96, position: 'absolute', background: '#0065AC'}} />
                            <div style={{width: 58.63, height: 58.41, left: 8.55, top: 9.12, position: 'absolute', background: '#0065AD'}} />
                            <div style={{width: 58.31, height: 58.10, left: 8.71, top: 9.27, position: 'absolute', background: '#0066AD'}} />
                            <div style={{width: 58, height: 57.79, left: 8.86, top: 9.43, position: 'absolute', background: '#0067AD'}} />
                            <div style={{width: 57.69, height: 57.48, left: 9.02, top: 9.58, position: 'absolute', background: '#0067AE'}} />
                            <div style={{width: 57.38, height: 57.17, left: 9.17, top: 9.74, position: 'absolute', background: '#0068AE'}} />
                            <div style={{width: 57.07, height: 56.86, left: 9.32, top: 9.89, position: 'absolute', background: '#0068AF'}} />
                            <div style={{width: 56.77, height: 56.56, left: 9.48, top: 10.05, position: 'absolute', background: '#0069AF'}} />
                            <div style={{width: 56.43, height: 56.23, left: 9.65, top: 10.20, position: 'absolute', background: '#006AB0'}} />
                            <div style={{width: 56.14, height: 55.93, left: 9.80, top: 10.36, position: 'absolute', background: '#006AB0'}} />
                            <div style={{width: 55.83, height: 55.62, left: 9.95, top: 10.51, position: 'absolute', background: '#006BB1'}} />
                            <div style={{width: 55.51, height: 55.31, left: 10.11, top: 10.67, position: 'absolute', background: '#006BB1'}} />
                            <div style={{width: 55.20, height: 55, left: 10.26, top: 10.82, position: 'absolute', background: '#006CB1'}} />
                            <div style={{width: 54.89, height: 54.69, left: 10.42, top: 10.98, position: 'absolute', background: '#006CB2'}} />
                            <div style={{width: 54.58, height: 54.38, left: 10.57, top: 11.13, position: 'absolute', background: '#006DB2'}} />
                            <div style={{width: 54.27, height: 54.07, left: 10.73, top: 11.29, position: 'absolute', background: '#006EB3'}} />
                            <div style={{width: 53.96, height: 53.76, left: 10.88, top: 11.44, position: 'absolute', background: '#006EB3'}} />
                            <div style={{width: 53.65, height: 53.45, left: 11.04, top: 11.60, position: 'absolute', background: '#006FB4'}} />
                            <div style={{width: 53.34, height: 53.14, left: 11.20, top: 11.75, position: 'absolute', background: '#0070B4'}} />
                            <div style={{width: 53.03, height: 52.83, left: 11.35, top: 11.91, position: 'absolute', background: '#0070B5'}} />
                            <div style={{width: 52.72, height: 52.52, left: 11.51, top: 12.07, position: 'absolute', background: '#0071B5'}} />
                            <div style={{width: 52.40, height: 52.21, left: 11.66, top: 12.22, position: 'absolute', background: '#0071B7'}} />
                            <div style={{width: 52.09, height: 51.90, left: 11.82, top: 12.37, position: 'absolute', background: '#0071B6'}} />
                            <div style={{width: 51.79, height: 51.60, left: 11.97, top: 12.53, position: 'absolute', background: '#0073B6'}} />
                            <div style={{width: 51.47, height: 51.28, left: 12.13, top: 12.68, position: 'absolute', background: '#0073B8'}} />
                            <div style={{width: 51.16, height: 50.97, left: 12.29, top: 12.84, position: 'absolute', background: '#0074B7'}} />
                            <div style={{width: 50.85, height: 50.66, left: 12.44, top: 12.99, position: 'absolute', background: '#0074B8'}} />
                            <div style={{width: 50.54, height: 50.35, left: 12.60, top: 13.15, position: 'absolute', background: '#0074B8'}} />
                            <div style={{width: 50.23, height: 50.05, left: 12.75, top: 13.30, position: 'absolute', background: '#0075B9'}} />
                            <div style={{width: 49.92, height: 49.73, left: 12.91, top: 13.46, position: 'absolute', background: '#0075B9'}} />
                            <div style={{width: 49.61, height: 49.42, left: 13.06, top: 13.61, position: 'absolute', background: '#0076B8'}} />
                            <div style={{width: 49.29, height: 49.11, left: 13.21, top: 13.77, position: 'absolute', background: '#0077BA'}} />
                            <div style={{width: 48.98, height: 48.80, left: 13.38, top: 13.92, position: 'absolute', background: '#0077BA'}} />
                            <div style={{width: 48.67, height: 48.49, left: 13.53, top: 14.08, position: 'absolute', background: '#0078BB'}} />
                            <div style={{width: 48.36, height: 48.18, left: 13.68, top: 14.23, position: 'absolute', background: '#0078BB'}} />
                            <div style={{width: 48.05, height: 47.87, left: 13.84, top: 14.39, position: 'absolute', background: '#0079BC'}} />
                            <div style={{width: 47.74, height: 47.57, left: 13.99, top: 14.54, position: 'absolute', background: '#007ABB'}} />
                            <div style={{width: 47.43, height: 47.25, left: 14.15, top: 14.70, position: 'absolute', background: '#007ABB'}} />
                            <div style={{width: 47.12, height: 46.95, left: 14.30, top: 14.86, position: 'absolute', background: '#007BBD'}} />
                            <div style={{width: 46.81, height: 46.64, left: 14.46, top: 15.01, position: 'absolute', background: '#007BBD'}} />
                            <div style={{width: 46.50, height: 46.32, left: 14.62, top: 15.16, position: 'absolute', background: '#007CBD'}} />
                            <div style={{width: 46.18, height: 46.01, left: 14.77, top: 15.32, position: 'absolute', background: '#007DBE'}} />
                            <div style={{width: 45.87, height: 45.71, left: 14.93, top: 15.47, position: 'absolute', background: '#007DBF'}} />
                            <div style={{width: 45.56, height: 45.40, left: 15.08, top: 15.63, position: 'absolute', background: '#007EBE'}} />
                            <div style={{width: 45.25, height: 45.09, left: 15.24, top: 15.78, position: 'absolute', background: '#007FC0'}} />
                            <div style={{width: 44.94, height: 44.78, left: 15.39, top: 15.94, position: 'absolute', background: '#007FC0'}} />
                            <div style={{width: 44.63, height: 44.47, left: 15.55, top: 16.09, position: 'absolute', background: '#0080C0'}} />
                            <div style={{width: 44.32, height: 44.16, left: 15.70, top: 16.25, position: 'absolute', background: '#0081C1'}} />
                            <div style={{width: 44.01, height: 43.85, left: 15.86, top: 16.40, position: 'absolute', background: '#0081C1'}} />
                            <div style={{width: 43.70, height: 43.54, left: 16.02, top: 16.55, position: 'absolute', background: '#0081C1'}} />
                            <div style={{width: 43.38, height: 43.23, left: 16.17, top: 16.71, position: 'absolute', background: '#0081C1'}} />
                            <div style={{width: 43.08, height: 42.92, left: 16.33, top: 16.87, position: 'absolute', background: '#0083C3'}} />
                            <div style={{width: 42.76, height: 42.61, left: 16.48, top: 17.02, position: 'absolute', background: '#0083C3'}} />
                            <div style={{width: 42.45, height: 42.30, left: 16.64, top: 17.18, position: 'absolute', background: '#0083C3'}} />
                            <div style={{width: 42.14, height: 41.99, left: 16.79, top: 17.32, position: 'absolute', background: '#0085C4'}} />
                            <div style={{width: 41.83, height: 41.67, left: 16.95, top: 17.49, position: 'absolute', background: '#0085C4'}} />
                            <div style={{width: 41.52, height: 41.37, left: 17.10, top: 17.64, position: 'absolute', background: '#0085C5'}} />
                            <div style={{width: 41.21, height: 41.06, left: 17.26, top: 17.80, position: 'absolute', background: '#0087C5'}} />
                            <div style={{width: 40.90, height: 40.75, left: 17.41, top: 17.95, position: 'absolute', background: '#0087C7'}} />
                            <div style={{width: 40.59, height: 40.44, left: 17.57, top: 18.11, position: 'absolute', background: '#0088C7'}} />
                            <div style={{width: 40.27, height: 40.13, left: 17.73, top: 18.26, position: 'absolute', background: '#0089C6'}} />
                            <div style={{width: 39.96, height: 39.82, left: 17.88, top: 18.42, position: 'absolute', background: '#0089C7'}} />
                            <div style={{width: 39.65, height: 39.51, left: 18.04, top: 18.57, position: 'absolute', background: '#008AC7'}} />
                            <div style={{width: 39.34, height: 39.20, left: 18.20, top: 18.73, position: 'absolute', background: '#008AC8'}} />
                            <div style={{width: 39.03, height: 38.89, left: 18.35, top: 18.88, position: 'absolute', background: '#008BC8'}} />
                            <div style={{width: 38.72, height: 38.58, left: 18.50, top: 19.04, position: 'absolute', background: '#008CCA'}} />
                            <div style={{width: 38.41, height: 38.27, left: 18.66, top: 19.19, position: 'absolute', background: '#008CCA'}} />
                            <div style={{width: 38.10, height: 37.96, left: 18.81, top: 19.34, position: 'absolute', background: '#008DCA'}} />
                            <div style={{width: 37.79, height: 37.65, left: 18.97, top: 19.50, position: 'absolute', background: '#008ECA'}} />
                            <div style={{width: 37.48, height: 37.34, left: 19.13, top: 19.66, position: 'absolute', background: '#008ECA'}} />
                            <div style={{width: 37.18, height: 37.04, left: 19.27, top: 19.80, position: 'absolute', background: '#008FCC'}} />
                            <div style={{width: 36.85, height: 36.71, left: 19.44, top: 19.97, position: 'absolute', background: '#0091CC'}} />
                            <div style={{width: 36.54, height: 36.41, left: 19.59, top: 20.12, position: 'absolute', background: '#0091CD'}} />
                            <div style={{width: 36.23, height: 36.10, left: 19.75, top: 20.27, position: 'absolute', background: '#0091CD'}} />
                            <div style={{width: 35.92, height: 35.79, left: 19.90, top: 20.43, position: 'absolute', background: '#0091CD'}} />
                            <div style={{width: 35.61, height: 35.48, left: 20.07, top: 20.59, position: 'absolute', background: '#0092CE'}} />
                            <div style={{width: 35.30, height: 35.17, left: 20.22, top: 20.74, position: 'absolute', background: '#0093CE'}} />
                            <div style={{width: 34.99, height: 34.86, left: 20.37, top: 20.90, position: 'absolute', background: '#0093CF'}} />
                            <div style={{width: 34.68, height: 34.55, left: 20.53, top: 21.05, position: 'absolute', background: '#0095CF'}} />
                            <div style={{width: 34.37, height: 34.24, left: 20.68, top: 21.20, position: 'absolute', background: '#0095D0'}} />
                            <div style={{width: 34.06, height: 33.93, left: 20.84, top: 21.36, position: 'absolute', background: '#0095D1'}} />
                            <div style={{width: 33.74, height: 33.62, left: 20.99, top: 21.52, position: 'absolute', background: '#0097D1'}} />
                            <div style={{width: 33.44, height: 33.31, left: 21.15, top: 21.67, position: 'absolute', background: '#0098D2'}} />
                            <div style={{width: 33.12, height: 33, left: 21.31, top: 21.83, position: 'absolute', background: '#0098D2'}} />
                            <div style={{width: 32.81, height: 32.69, left: 21.46, top: 21.98, position: 'absolute', background: '#0099D3'}} />
                            <div style={{width: 32.50, height: 32.38, left: 21.61, top: 22.14, position: 'absolute', background: '#0099D3'}} />
                            <div style={{width: 32.20, height: 32.08, left: 21.76, top: 22.28, position: 'absolute', background: '#009AD3'}} />
                            <div style={{width: 31.87, height: 31.75, left: 21.93, top: 22.45, position: 'absolute', background: '#009BD5'}} />
                            <div style={{width: 31.57, height: 31.45, left: 22.08, top: 22.60, position: 'absolute', background: '#009BD5'}} />
                            <div style={{width: 31.25, height: 31.14, left: 22.24, top: 22.75, position: 'absolute', background: '#00A9EA'}} />
                            <div style={{width: 39.13, height: 38.99, left: 18.30, top: 18.83, position: 'absolute', background: '#090406'}} />
                            <div style={{width: 17.22, height: 17.16, left: 16.23, top: 11.25, position: 'absolute', background: 'white'}} />
                            <div style={{width: 32, height: 33.40, left: 92.56, top: 9.83, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 6.27, height: 34.75, left: 130.84, top: 8.14, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 26.40, height: 24.79, left: 142.41, top: 18.68, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 25.97, height: 35.35, left: 173.92, top: 8.14, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 21.88, height: 24.80, left: 203.39, top: 18.68, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 6.27, height: 34.75, left: 231.73, top: 8.14, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 24.31, height: 33.88, left: 243.75, top: 9.59, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 7.45, height: 34.71, left: 273.08, top: 8.18, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 25.23, height: 34.54, left: 285.24, top: 18.66, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 23.36, height: 24.22, left: 317.11, top: 18.68, position: 'absolute', background: '#004DA0'}} />
                            <div style={{width: 9.10, height: 9.07, left: 347.03, top: 33.79, position: 'absolute', background: '#090406'}} />
                            <div style={{width: 4.56, height: 5.17, left: 349.53, top: 35.66, position: 'absolute', background: '#090406'}} />
                        </div>
                        <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 54, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', textAlign: 'center'}}><span style="color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'">国内シェア・認知度No.1</span><sup style="color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'">※</sup><span style="color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'">のSSL、クライアント認証、IDaaS</span></div>
                                <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>電子証明書と認証サービスで、お客様ビジネスの安全性・信頼性を高めます</div>
                            </div>
                            <div style={{width: 455.37, textAlign: 'center', color: 'white', fontSize: 14, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                        </div>
                        <div style={{width: 1512, height: 93, background: 'white', border: '1px #5A5858 solid'}} />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <img style={{width: 99.67, height: 39.87}} src="https://placehold.co/100x40" />
                        <div style={{alignSelf: 'stretch', boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 22, display: 'inline-flex'}}>
                                <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                    <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ウェブサイトのセキュリティ強化</div>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>SSLサーバ証明書</div>
                                    </div>
                                </div>
                                <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                    <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>組織の不正アクセス対策</div>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>マネージドPKI Lite byGMO</div>
                                    </div>
                                </div>
                                <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                    <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>情報漏洩・不正アクセス対策</div>
                                        <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>GMOトラスト・ログイン</div>
                                    </div>
                                </div>
                            </div>
                            <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>GMOグローバルサインの「SSLサーバ証明書」は、選ばれ続けて累計2,800万枚以上の発行実績。常時SSLにも対応、3種類のSSL認証レベルと豊富な運用コストダウンプランで、ユーザが安心できるウェブサイトの構築をサポートします。</div>
                                    <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                        <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                        <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                            <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                    </div>
                                </div>
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>マネージドPKI Lite byGMOは、個人や組織を認証し発行される「クライアント証明書」の一括管理が可能なSaaS型CAソリューション。 既存システムとの高い親和性で、様々な業種や組織の情報セキュリティを強化します。</div>
                                    <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                        <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                        <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                            <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                    </div>
                                </div>
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>GMOトラスト・ログインは増え続けるID・パスワードを一元管理して、セキュアな業務環境をつくる国産IDaaSです。シングルサインオン、アクセス制限、認証強化などゼロトラストの基盤となるID管理、認証を行い情報漏洩・不正アクセス対策に貢献します。</div>
                                    <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                        <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                        <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                            <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style={{width: 588, justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div data-プロパティ1="デフォルト" style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>公式サイト</div>
                                <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                </div>
                            </div>
                            <div data-プロパティ1="デフォルト" style={{width: 282, borderRadius: 60, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
                                <div style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, background: '#08F2BA', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                    <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>お問い合わせ</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>※外部調査会社によるWebサイト担当者へのアンケート調査結果（2022年12月現在）</div>
                </div>
                <div style={{alignSelf: 'stretch', paddingLeft: 102, paddingRight: 102, paddingTop: 64, paddingBottom: 64, background: '#F2F3F4', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）新着記事</div>
                        <div style={{alignSelf: 'stretch', height: 416, justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'inline-flex'}}>
                                <img style={{alignSelf: 'stretch', height: 274, borderTopLeftRadius: 12, borderTopRightRadius: 12}} src="https://placehold.co/486x274" />
                                <div style={{alignSelf: 'stretch', flex: '1 1 0', padding: 24, background: 'var(--color-neutral-100, white)', borderBottomRightRadius: 12, borderBottomLeftRadius: 12, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2023.08.08</div>
                                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>「私はロボットではありません」が表示される原因｜突破方法や回避方法</div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-プロパティ1="デフォルト" style={{alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
                        <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）記事一覧</div>
                        <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                            <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
            <div style={{alignSelf: 'stretch', paddingTop: 60, paddingBottom: 60, background: 'white', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                <div style={{paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                    <div style={{textAlign: 'center', color: 'white', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>サイバー攻撃対策（サイバーセキュリティ）</div>
                </div>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 45, display: 'flex'}}>
                    <div style={{width: 516, height: 77.22, position: 'relative', overflow: 'hidden'}}>
                        <div style={{width: 516, height: 77.22, left: 0, top: 0, position: 'absolute', background: 'black'}} />
                        <div style={{width: 36.78, height: 30.14, left: 171.54, top: 47.09, position: 'absolute', background: '#009FA5'}} />
                        <div style={{width: 101.32, height: 25.72, left: 221.45, top: 49.22, position: 'absolute', background: '#021725'}} />
                        <div style={{width: 341.47, height: 33.29, left: 174.55, top: 0.40, position: 'absolute', background: '#5A5858'}} />
                        <div style={{width: 167.03, height: 34.24, left: 0, top: 0, position: 'absolute', background: '#005BAC'}} />
                    </div>
                    <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 54, display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>世界トップレベルのホワイトハッカーがセキュリティリスクを評価</div>
                        </div>
                        <div style={{width: 455.37, textAlign: 'center', color: 'white', fontSize: 14, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                    </div>
                    <img style={{width: 1200, height: 247}} src="https://placehold.co/1200x247" />
                    <div style={{alignSelf: 'stretch', boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 22, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>脆弱性診断・<br/>ペネトレーションテスト</div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>GMOサイバー攻撃<br/>ネットde診断</div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>サイバー攻撃防御・分析</div>
                                </div>
                            </div>
                        </div>
                        <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>世界トップレベルのホワイトハッカーが調査することによって、標準的な脆弱性診断検出ができないような脆弱性も検出し、リスクを評価します。</div>
                                <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>当社の世界屈指の知見を活用したASMツール、脆弱性診断ツールにより、定期的な脆弱性診断の内製化を実現します。</div>
                                <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>WAFなどのセキュリティ製品の導入後、運用やアラート検知後の対応にお困りのセキュリティ担当者様に対し、自動運用サービスやSOCサービスをご提供します。</div>
                                <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 30, wordWrap: 'break-word'}}>Webサービスやアプリにおけるセキュリティ上の問題点を解消し、<br/>収益の最大化を実現する相談役としてぜひお気軽にご連絡ください。</div>
                    <div style={{width: 588, justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                        <div data-プロパティ1="デフォルト" style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                            <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>公式サイト</div>
                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            </div>
                        </div>
                        <div data-プロパティ1="デフォルト" style={{width: 282, borderRadius: 60, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
                            <div style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, background: '#08F2BA', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>お問い合わせ</div>
                                <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>※外部調査会社によるWebサイト担当者へのアンケート調査結果（2022年12月現在）</div>
            </div>
            <div style={{alignSelf: 'stretch', paddingLeft: 102, paddingRight: 102, paddingTop: 64, paddingBottom: 64, background: '#F2F3F4', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>サイバー攻撃対策（サイバーセキュリティ）新着記事</div>
                    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                        <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'inline-flex'}}>
                            <img style={{alignSelf: 'stretch', height: 274, borderTopLeftRadius: 12, borderTopRightRadius: 12}} src="https://placehold.co/486x274" />
                            <div style={{alignSelf: 'stretch', height: 118, paddingTop: 16, paddingBottom: 24, paddingLeft: 24, paddingRight: 24, background: 'var(--color-neutral-100, white)', borderBottomRightRadius: 12, borderBottomLeftRadius: 12, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2023.08.08</div>
                                <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>「私はロボットではありません」が表示される原因｜突破方法や回避方法</div>
                            </div>
                        </div>
                        <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                            <div style={{alignSelf: 'stretch', height: 115, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                <img style={{width: 204, height: 115}} src="https://placehold.co/204x115" />
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                        <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                    </div>
                                </div>
                            </div>
                            <div style={{alignSelf: 'stretch', height: 115, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x115" />
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                        <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                    </div>
                                </div>
                            </div>
                            <div style={{alignSelf: 'stretch', height: 115, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x115" />
                                <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                        <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-プロパティ1="デフォルト" style={{alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
                    <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>サイバー攻撃対策（サイバーセキュリティ）記事一覧</div>
                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                    </div>
                </div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
            <div style={{alignSelf: 'stretch', paddingTop: 60, paddingBottom: 60, background: 'white', borderRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                <div style={{paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                    <div style={{textAlign: 'center', color: 'white', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>サイバー攻撃対策（サイバーセキュリティ）</div>
                </div>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 45, display: 'flex'}}>
                    <div style={{width: 500, height: 54, position: 'relative', overflow: 'hidden'}}>
                        <div style={{width: 55.17, height: 33.63, left: 57.07, top: 9.49, position: 'absolute', background: '#005BAC'}} />
                        <div style={{width: 57.42, height: 34.53, left: 0.30, top: 9.03, position: 'absolute', background: '#005BAC'}} />
                        <div style={{width: 57.42, height: 34.53, left: 111.59, top: 9.04, position: 'absolute', background: '#005BAC'}} />
                        <div style={{width: 36.01, height: 29.12, left: 259.82, top: 14.45, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 31.38, height: 33.31, left: 303.47, top: 10.26, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 25.57, height: 24.35, left: 363, top: 19.22, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 21.84, height: 23.86, left: 417.27, top: 19.20, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 15.14, height: 33.05, left: 436.80, top: 10.02, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 47.45, height: 39.38, left: 452.26, top: 14.45, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 29.08, height: 23.86, left: 389.23, top: 19.73, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 42.57, height: 32.29, left: 176.79, top: 10.76, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 28.61, height: 24.35, left: 228.57, top: 19.22, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 27.23, height: 24.36, left: 333.67, top: 19.21, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 15.35, height: 34.03, left: 214.86, top: 9.04, position: 'absolute', background: '#D22550'}} />
                        <div style={{width: 499.40, height: 53.83, left: 0.30, top: 0, position: 'absolute'}} />
                    </div>
                    <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 54, display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>エンジニアの背中を預かる</div>
                            <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>ソフトウェア・プロダクトの開発組織とそこで働くエンジニアにとって最適な<br/>セキュリティサービスを提供し、「背中を預けられる」存在になることが使命です。</div>
                        </div>
                        <div style={{width: 455.37, textAlign: 'center', color: 'white', fontSize: 14, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                    </div>
                    <img style={{width: 1512, height: 137}} src="https://placehold.co/1512x137" />
                    <div style={{alignSelf: 'stretch', boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 22, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>脆弱性診断</div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{width: 252, textAlign: 'center'}}><span style="color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'">Shisho Cloud<br/></span><span style="color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 30, wordWrap: 'break-word'">（シショウ クラウド）</span></div>
                                </div>
                            </div>
                        </div>
                        <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>ブラックボックス形式だけに頼らない独自の診断スタイル、高度な技術力、モダンな技術スタックへの対応、開発者目線の丁寧なレポーティングで他社が追随できない開発者体験を実現します。</div>
                                <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>Shisho Cloud(シショウ クラウド)は、脆弱性診断を誰でもまるごと内製化できるSaaSです。 WebアプリケーションやAWS等のクラウドを網羅的に診断し、継続的なリスク管理を実現します。</div>
                                <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                    <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 30, wordWrap: 'break-word'}}>Webサービスやアプリにおけるセキュリティ上の問題点を解消し、<br/>収益の最大化を実現する相談役としてぜひお気軽にご連絡ください。</div>
                    <div style={{width: 588, justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                        <div data-プロパティ1="デフォルト" style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                            <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>公式サイト</div>
                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            </div>
                        </div>
                        <div data-プロパティ1="デフォルト" style={{width: 282, borderRadius: 60, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
                            <div style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, background: '#08F2BA', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>お問い合わせ</div>
                                <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>※外部調査会社によるWebサイト担当者へのアンケート調査結果（2022年12月現在）</div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                <div style={{alignSelf: 'stretch', paddingTop: 60, paddingBottom: 60, background: 'white', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                    <div style={{paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                        <div style={{textAlign: 'center', color: 'white', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>なりすまし対策（ブランドセキュリティ）</div>
                    </div>
                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 45, display: 'flex'}}>
                        <div style={{width: 482.29, height: 32, position: 'relative', overflow: 'hidden'}}>
                            <div style={{width: 19.33, height: 29.50, left: 162.80, top: 1.18, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 21.77, height: 29.47, left: 184.80, top: 1.19, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 27.20, height: 30, left: 209.40, top: 0.62, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 25.71, height: 30.54, left: 239.58, top: 0.62, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 27.14, height: 29.46, left: 269.06, top: 1.18, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 17.76, height: 30.56, left: 303.31, top: 0.61, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 15.73, height: 29.47, left: 323.81, top: 1.17, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 25.45, height: 30.57, left: 341.81, top: 0.63, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 25.37, height: 30.03, left: 370.87, top: 1.16, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 21.77, height: 29.47, left: 399.55, top: 1.19, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 4.62, height: 29.47, left: 424.07, top: 1.18, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 22.25, height: 29.49, left: 431.61, top: 1.17, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 24.39, height: 29.48, left: 456.83, top: 1.17, position: 'absolute', background: '#595757'}} />
                            <div style={{width: 50.34, height: 30.63, left: 52.81, top: 0.67, position: 'absolute', background: '#005BAC'}} />
                            <div style={{width: 52.37, height: 31.45, left: 1.04, top: 0.26, position: 'absolute', background: '#005BAC'}} />
                            <div style={{width: 52.37, height: 31.45, left: 102.54, top: 0.26, position: 'absolute', background: '#005BAC'}} />
                        </div>
                        <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 54, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>すべてのブランドにセキュリティを</div>
                                <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>安心・安全なブランドであることが、ブランドロイヤリティ獲得の第一歩です。</div>
                            </div>
                            <div style={{width: 455.37, textAlign: 'center', color: 'white', fontSize: 14, fontFamily: 'Hiragino Sans Pr6N', fontWeight: '600', lineHeight: 20, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                        </div>
                        <img style={{width: 1200, height: 284}} src="https://placehold.co/1200x284" />
                        <div style={{width: 266, height: 79, background: 'white'}} />
                        <div style={{width: 303, height: 79, background: 'white'}} />
                        <div style={{width: 485, height: 27, background: 'white'}} />
                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 22, display: 'inline-flex'}}>
                                    <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                        <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>ブランドセキュリティ</div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                        <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>ドメインネーム</div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                        <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>商標</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                    <div style={{flex: '1 1 0', height: 210, padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>ウェブサイト（ドメインネーム含む）、SNS、マーケットプレイスにおけるブランド侵害監視、エンフォースメント支援、NFT・Web3.0など</div>
                                        <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                            <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            </div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>ドメインネームの調査・取得・管理、SSL取得・管理、マネジドDNSの提供、ガイドライン策定、教育訓練など</div>
                                        <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                            <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            </div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>IPランドスケープ（競合他社分析など）、先行類似調査・出願/登録・更新などの手続き支援、商標ウォッチングなど</div>
                                        <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                            <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style={{width: 792, boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.10)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 22, display: 'inline-flex'}}>
                                    <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                        <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>ブランドTLD</div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', paddingLeft: 20, paddingRight: 20, paddingTop: 18, paddingBottom: 18, background: '#005BAC', overflow: 'hidden', borderTopLeftRadius: 8, borderTopRightRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                                        <div style={{flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{width: 252, textAlign: 'center', color: 'white', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>IPv4</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>ブランドTLDの申請・運用支援、活用支援（レガシーTLDからの移行、マンスリレポート提供など）、ガイドライン策定、教育訓練など</div>
                                        <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                            <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            </div>
                                        </div>
                                    </div>
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', padding: 24, background: 'white', overflow: 'hidden', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>IPv4アドレスの売買支援（国際移転含む）</div>
                                        <div style={{width: 90, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                                            <div style={{color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>詳細を見る</div>
                                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style={{width: 588, justifyContent: 'center', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div data-プロパティ1="デフォルト" style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>公式サイト</div>
                                <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                    <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                </div>
                            </div>
                            <div data-プロパティ1="デフォルト" style={{width: 282, borderRadius: 60, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
                                <div style={{flex: '1 1 0', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, background: '#08F2BA', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 16, display: 'flex'}}>
                                    <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>お問い合わせ</div>
                                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>※外部調査会社によるWebサイト担当者へのアンケート調査結果（2022年12月現在）</div>
                </div>
                <div style={{alignSelf: 'stretch', paddingLeft: 102, paddingRight: 102, paddingTop: 64, paddingBottom: 64, background: '#F2F3F4', borderBottomRightRadius: 8, borderBottomLeftRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#021725', fontSize: 28, fontFamily: 'Noto Sans JP', fontWeight: '600', lineHeight: 42, wordWrap: 'break-word'}}>なりすまし対策（ブランドセキュリティ）新着記事</div>
                        <div style={{alignSelf: 'stretch', height: 416, justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'inline-flex'}}>
                                <img style={{alignSelf: 'stretch', height: 274, borderTopLeftRadius: 12, borderTopRightRadius: 12}} src="https://placehold.co/486x274" />
                                <div style={{alignSelf: 'stretch', flex: '1 1 0', padding: 24, background: 'var(--color-neutral-100, white)', borderBottomRightRadius: 12, borderBottomLeftRadius: 12, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2023.08.08</div>
                                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>「私はロボットではありません」が表示される原因｜突破方法や回避方法</div>
                                </div>
                            </div>
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                                <div style={{alignSelf: 'stretch', height: 122.67, background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                                    <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x123" />
                                    <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-プロパティ1="デフォルト" style={{alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
                        <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>なりすまし対策（ブランドセキュリティ）記事一覧</div>
                        <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                            <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style={{textAlign: 'center', color: '#26FF0E', fontSize: 36, fontFamily: 'Inter', fontWeight: '700', lineHeight: 54, wordWrap: 'break-word'}}>端まで←</div>
        <div style={{textAlign: 'center', color: '#26FF0E', fontSize: 36, fontFamily: 'Inter', fontWeight: '700', lineHeight: 54, wordWrap: 'break-word'}}>→端まで</div>
        <div style={{textAlign: 'center', color: '#26FF0E', fontSize: 36, fontFamily: 'Inter', fontWeight: '700', lineHeight: 54, wordWrap: 'break-word'}}>端まで←</div>
        <div style={{textAlign: 'center', color: '#26FF0E', fontSize: 36, fontFamily: 'Inter', fontWeight: '700', lineHeight: 54, wordWrap: 'break-word'}}>→端まで</div>
    </div>
    <div style={{paddingLeft: 156, paddingRight: 156, background: 'var(--color-neutral-50, #F2F3F4)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 10, display: 'flex'}}>
        <div style={{width: 1200, maxWidth: 1200, paddingLeft: 102, paddingRight: 102, paddingTop: 96, paddingBottom: 96, overflow: 'hidden', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 64, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 48, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#008F79', fontSize: 48, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 72, wordWrap: 'break-word'}}>セキュリティ全般</div>
                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 30, wordWrap: 'break-word'}}>インターネットをより安心・安全に利用いただくために、セキュリティに関する情報を発信しています。</div>
                </div>
                <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                    <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'inline-flex'}}>
                        <img style={{alignSelf: 'stretch', height: 274, borderTopLeftRadius: 12, borderTopRightRadius: 12}} src="https://placehold.co/486x274" />
                        <div style={{alignSelf: 'stretch', padding: 24, background: 'var(--color-neutral-100, white)', borderBottomRightRadius: 12, borderBottomLeftRadius: 12, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2023.08.08</div>
                            <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>「私はロボットではありません」が表示される原因｜突破方法や回避方法</div>
                        </div>
                    </div>
                    <div style={{flex: '1 1 0', alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', flex: '1 1 0', background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                            <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x115" />
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                </div>
                            </div>
                        </div>
                        <div style={{alignSelf: 'stretch', flex: '1 1 0', background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                            <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x115" />
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                </div>
                            </div>
                        </div>
                        <div style={{alignSelf: 'stretch', flex: '1 1 0', background: 'var(--color-neutral-100, white)', overflow: 'hidden', borderRadius: 12, justifyContent: 'flex-start', alignItems: 'center', display: 'inline-flex'}}>
                            <img style={{width: 204, alignSelf: 'stretch'}} src="https://placehold.co/204x115" />
                            <div style={{flex: '1 1 0', alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 8, paddingBottom: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                                    <div style={{alignSelf: 'stretch', color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>2022.11.12</div>
                                    <div style={{alignSelf: 'stretch', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>システムに侵入したペンテスターが、よく目にする脆弱性とは？</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-プロパティ1="デフォルト" style={{alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24, paddingTop: 20, paddingBottom: 20, borderRadius: 60, outline: '1px var(--color-neutral-900, #021725) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
                    <div style={{textAlign: 'center', color: 'var(--color-neutral-900, #021725)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>記事一覧</div>
                    <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                        <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                        <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                    </div>
                </div>
            </div>
        </div>
        <div style={{width: 64, paddingTop: 20, paddingBottom: 20, background: '#005BAC', overflow: 'hidden', borderRadius: 40, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
            <div style={{textAlign: 'center', color: 'white', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 24, wordWrap: 'break-word'}}>新着</div>
        </div>
    </div>
    <div style={{width: 1512, paddingLeft: 156, paddingRight: 156, paddingTop: 96, paddingBottom: 96, background: 'white', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
        <div style={{width: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 64, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                <div style={{flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
                    <div style={{width: 180, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
                        <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                            <div style={{width: 180, height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', flex: '1 1 0', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>全記事一覧</div>
                            </div>
                        </div>
                    </div>
                    <div style={{width: 180, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
                        <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                            <div style={{width: 180, height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                                <div style={{alignSelf: 'stretch', flex: '1 1 0', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>サイトシール</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style={{width: 180, alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
                    <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>セキュリティ全般</div>
                        </div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>セキュリティ 対策</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>セキュリティ 警告</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ゼロトラスト</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>情報 セキュリティ</div>
                    </div>
                </div>
                <div style={{width: 180, alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
                    <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>実在証明・盗聴対策</div>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>（暗号セキュリティ）</div>
                        </div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>人気記事</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>http https</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>この接続ではプライバシーが保護されません</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>保護されていない通信</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>暗号化</div>
                    </div>
                </div>
                <div style={{width: 180, alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
                    <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>サイバー攻撃対策</div>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>（サイバーセキュリティ）</div>
                        </div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>人気記事</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>SOC</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>サイバー攻撃</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ホワイトハッカーとは</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>マルウェアとは</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>侵入テスト（ペネトレーションテスト）</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>脆弱性診断（セキュリティ診断）</div>
                    </div>
                </div>
                <div style={{width: 180, alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
                    <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>なりすまし対策</div>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>（ブランドセキュリティ）</div>
                        </div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>人気記事</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>DNSとは</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ドメイン 乗っ取り</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ドメイン管理</div>
                    </div>
                </div>
                <div style={{width: 180, alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
                    <div style={{width: 180, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
                        <div style={{alignSelf: 'stretch', height: 42, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
                            <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 16, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 24, wordWrap: 'break-word'}}>ニュース</div>
                        </div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>人気記事</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>ニュース全般</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>サイバー攻撃対策（サイバーセキュリティ）</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>なりすまし対策（ブランドセキュリティ）</div>
                        <div style={{alignSelf: 'stretch', color: '#021725', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>実在証明・盗聴対策（暗号セキュリティ）</div>
                    </div>
                </div>
            </div>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                <div style={{width: 1199.69, justifyContent: 'space-between', alignItems: 'flex-start', display: 'inline-flex'}}>
                    <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'flex'}}>
                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'flex'}}>
                            <div style={{color: '#333333', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>利用規約</div>
                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            </div>
                        </div>
                        <div style={{width: 1.15, height: 20, background: '#C8C8C8'}} />
                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'flex'}}>
                            <div style={{color: '#333333', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>プライバシーポリシー</div>
                            <div style={{width: 14, height: 11, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 11, height: 8, left: 0.50, top: 2.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                                <div style={{width: 10, height: 7, left: 3.50, top: 0.50, position: 'absolute', outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                            </div>
                        </div>
                    </div>
                    <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 32, display: 'flex'}}>
                        <div style={{width: 79.62, color: '#021725', fontSize: 20, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 30, wordWrap: 'break-word'}}>SHARE</div>
                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'flex'}}>
                            <div style={{width: 40, height: 40, background: '#021725', borderRadius: 9999}} />
                            <div style={{width: 21.60, height: 22.10, background: 'white'}} />
                            <div style={{width: 40, height: 40, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 40, height: 39.85, left: 0, top: 0, position: 'absolute', background: '#0866FF'}} />
                                <div style={{width: 18.24, height: 32.60, left: 11.04, top: 7.40, position: 'absolute', background: 'white'}} />
                            </div>
                            <div style={{width: 40, height: 40, position: 'relative', overflow: 'hidden'}}>
                                <div style={{width: 40, height: 40, left: 0, top: 0, position: 'absolute', background: '#4CC764'}} />
                                <div style={{width: 26.70, height: 25.44, left: 6.64, top: 8, position: 'absolute', background: 'white'}} />
                                <div style={{width: 4.26, height: 6.34, left: 24.89, top: 15.95, position: 'absolute', background: '#4CC764'}} />
                                <div style={{width: 4.26, height: 6.34, left: 11.02, top: 15.95, position: 'absolute', background: '#4CC764'}} />
                                <div style={{width: 1.46, height: 6.34, left: 16.08, top: 15.95, position: 'absolute', background: '#4CC764'}} />
                                <div style={{width: 5.53, height: 6.34, left: 18.45, top: 15.95, position: 'absolute', background: '#4CC764'}} />
                            </div>
                        </div>
                    </div>
                    <div style={{width: 24, height: 24, position: 'relative', borderRadius: 200}} />
                </div>
                <div style={{width: 366.92, color: '#5A5858', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 18, wordWrap: 'break-word'}}>Copyright © 2022 GMO Internet Group, Inc. All Rights Reserved.</div>
            </div>
        </div>
    </div>
    <div style={{width: 1512, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', display: 'flex'}}>
        <div style={{alignSelf: 'stretch', paddingTop: 24, paddingBottom: 40, background: '#FAFAFA', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'flex'}}>
            <div style={{width: '100%', maxWidth: 1200, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 19, display: 'flex'}}>
                    <div style={{paddingLeft: 10, paddingRight: 10, justifyContent: 'center', alignItems: 'center', gap: 20, display: 'inline-flex', flexWrap: 'wrap', alignContent: 'center'}}>
                        <div style={{width: 281.60, height: 17.60, background: 'var(--GMO_blue, #005BAC)'}} />
                        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
                            <div style={{width: 20, height: 19.93, background: '#4D4D4D'}} />
                            <div style={{width: 9.12, height: 16.30}} />
                            <div style={{width: 18, height: 17, background: '#4D4D4D'}} />
                            <div style={{width: 20, height: 14, background: '#4D4D4D'}} />
                            <div style={{width: 19, height: 19, background: '#4D4D4D'}} />
                            <div style={{width: 16, height: 19, background: '#4D4D4D'}} />
                        </div>
                    </div>
                    <div style={{alignSelf: 'stretch', height: 1, background: '#C8C8C8'}} />
                    <div style={{alignSelf: 'stretch', paddingLeft: 10, paddingRight: 10, paddingTop: 3, paddingBottom: 3, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 10, display: 'inline-flex', flexWrap: 'wrap', alignContent: 'flex-end'}}>
                        <div style={{width: 633, textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#4D4D4D', fontSize: 12, fontFamily: 'Inter', fontWeight: '700', wordWrap: 'break-word'}}>GMOインターネットグループのセキュリティ事業について</div>
                        <div style={{width: 800, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'flex', flexWrap: 'wrap', alignContent: 'center'}}>
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>世界初総合ネットセキュリティサービス「GMOセキュリティ24」</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>パスワード漏えい診断</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>Webサイトリスク診断</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>セキュリティ相談AIチャットボット</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>盗聴・改ざん対策</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>サイバー攻撃対策（GMOサイバーセキュリティ byイエラエ）</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>サイバー攻撃対策（GMO Flatt Security）</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>なりすまし対策</div>
                            <div style={{width: 1, height: 10, background: '#CCCCCC'}} />
                            <div style={{textBoxTrim: 'trim-both', textBoxEdge: 'cap alphabetic', color: '#428BCA', fontSize: 10, fontFamily: 'Inter', fontWeight: '400', wordWrap: 'break-word'}}>セキュリティ事業の軌跡</div>
                        </div>
                    </div>
                </div>
                <div style={{justifyContent: 'center', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
                    <img style={{width: 100, height: 50, position: 'relative'}} src="https://placehold.co/100x50" />
                    <img style={{width: 100, height: 50, position: 'relative'}} src="https://placehold.co/100x50" />
                    <img style={{width: 100, height: 50, position: 'relative'}} src="https://placehold.co/100x50" />
                </div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', height: 179, paddingLeft: 446, paddingRight: 446, paddingTop: 72, paddingBottom: 72, background: 'linear-gradient(359deg, #13D4A4 0%, #0B4D4B 39%, #0B4D4B 63%, #13D4A4 97%)', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'flex'}}>
            <div style={{justifyContent: 'center', alignItems: 'center', gap: 72, display: 'inline-flex'}}>
                <div style={{width: 529.99, height: 33, background: 'black'}} />
                <div style={{width: 184.62, height: 29.92, background: '#FFFF00'}} />
                <div style={{width: 529.98, height: 33.02, background: 'white'}} />
                <div style={{paddingLeft: 27, paddingRight: 27, paddingTop: 6, paddingBottom: 6, background: '#08F2BA', borderRadius: 20, justifyContent: 'center', alignItems: 'center', gap: 12, display: 'flex'}}>
                    <div style={{color: '#021725', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 23, wordWrap: 'break-word'}}>無料診断</div>
                    <div style={{width: 5, height: 10, outline: '1px #021725 solid', outlineOffset: '-0.50px'}} />
                </div>
            </div>
        </div>
        <div style={{alignSelf: 'stretch', height: 107, paddingBottom: 20, background: 'white', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 20, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', height: 1, background: 'var(--line, #C8C8C8)'}} />
            <div style={{width: '100%', maxWidth: 1200, paddingLeft: 10, paddingRight: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 16, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', justifyContent: 'space-between', alignItems: 'center', display: 'inline-flex'}}>
                    <div style={{width: 416, justifyContent: 'flex-start', alignItems: 'center', gap: 16, display: 'flex'}}>
                        <div style={{width: 176, height: 11, background: 'var(--GMO_blue, #005BAC)'}} />
                    </div>
                    <div style={{width: 480, height: 12, background: 'var(--GMO_blue, #005BAC)'}} />
                </div>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 2, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', color: 'var(--GMO_gray, #5A5858)', fontSize: 12, fontFamily: 'Noto Sans JP', fontWeight: '700', lineHeight: 20, wordWrap: 'break-word'}}>グループサービス</div>
                    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>インターネットサービス</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>ネットショップ・EC支援</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>ビジネスを支援</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>セキュリティ</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>マーケティング支援</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>リサーチ</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>情報収集</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>ネット金融</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>暗号資産</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>個人向けサービス</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                        <div style={{justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: 'var(--txt_link, #428BCA)', fontSize: 10, fontFamily: 'Noto Sans JP', fontWeight: '400', lineHeight: 16, wordWrap: 'break-word'}}>その他</div>
                            <div style={{width: 6, height: 3, background: 'var(--line, #C8C8C8)'}} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-プロパティ1="デフォルト" style={{width: 1512, paddingTop: 16, paddingLeft: 24, paddingRight: 24, left: 0, top: 28, position: 'absolute', justifyContent: 'flex-end', alignItems: 'center', display: 'inline-flex'}}>
        <div style={{paddingLeft: 16, paddingRight: 16, paddingTop: 14, paddingBottom: 14, justifyContent: 'center', alignItems: 'center', gap: 10, display: 'flex'}}>
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 4, height: 4, background: 'var(--color-securityGreen-600, #009080)', borderRadius: 9999}} />
            <div style={{width: 24.10, height: 19, background: '#021725'}} />
            <div style={{width: 23.25, height: 18.16, background: '#FFF000'}} />
        </div>
    </div>
</div>