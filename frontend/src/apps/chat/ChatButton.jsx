import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MessageList,
  Message,
  MessageInput,
} from '@chatscope/chat-ui-kit-react';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import Button from '../../common/components/Button';
import useChatHandler from '../../common/hooks/useChatHandler';
import { ChatMessageBubbleMarkDown } from './ChatMessageBubbleMarkDown';

export const ChatButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  const [messages, setMessages] = useState([
    {
      role: 'system',
      content:
        'セキュリティで懸念されている点について、お気軽にお尋ねください。',
    },
  ]);
  const [threadTs, setThreadTs] = useState(null);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isSending, setIsSending] = useState(false);
  const [inputText, setInputText] = useState('');

  const location = useLocation();
  const {
    isChatValidAndAllowed,
    incrementChatCounter,
    clearChatErrors,
    chatError,
  } = useChatHandler();

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  const handleInputChange = (_, textContent) => {
    clearChatErrors();
    setInputText(textContent);
  };

  const handleSend = async (_, post) => {
    if (!isChatValidAndAllowed(post)) {
      return;
    }

    const oldMessages = messages;
    setMessages([...oldMessages, { role: 'user', content: post }]);
    setInputText('');
    setIsSending(true);
    incrementChatCounter();
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );

      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/chat`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            post,
            messages: oldMessages
              .filter(({ role }) => ['user', 'assistant'].includes(role))
              .map(m => ({ role: m.role, content: m.content })),
            threadTs,
            recaptchaToken,
            path: location.pathname,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let hasNewLineOccurred = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        if (!hasNewLineOccurred && buffer.includes('\n')) {
          const [messageBeforeNewLine, ...rest] = buffer.split('\n');
          setThreadTs(messageBeforeNewLine);
          buffer = rest.join('\n');
          hasNewLineOccurred = true;
        }
        if (hasNewLineOccurred && buffer.length > 0) {
          setMessages([
            ...oldMessages,
            { role: 'user', content: post },
            { role: 'assistant', content: buffer },
          ]);
          setIsSending(false);
        }
      }
      setMessages([
        ...oldMessages,
        { role: 'user', content: post },
        {
          role: 'assistant',
          content: buffer,
          feedback: null,
          replyNumber: oldMessages.filter(({ role }) => role === 'assistant')
            .length,
        },
      ]);
      reader.releaseLock();
    } catch (err) {
      console.error(err);
    } finally {
      setIsSending(false);
    }
  };

  const handleFeedback = async (replyNumber, feedback) => {
    try {
      let newFeedback = feedback;
      const newMessages = messages.map((m) => {
        if (m.role !== 'assistant') return m;
        if (m.replyNumber !== replyNumber) return m;

        if (m.feedback === feedback) {
          newFeedback = null;
        }
        m.feedback = newFeedback;
        return m;
      });
      if (newFeedback === 'bad') {
        newMessages.push({
          role: 'system',
          content:
            'ご期待にお応えできず申し訳ございません。よろしければさらに詳しくお聞かせいただけませんでしょうか。',
        });
      }
      setMessages(newMessages);

      if (!threadTs) return;
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/feedback`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            threadTs,
            replyNumber,
            feedback: newFeedback,
            recaptchaToken,
          }),
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleMaximized = () => {
    const currentState = JSON.parse(localStorage.getItem('state'));
    localStorage.setItem(
      'state',
      JSON.stringify({ ...currentState, messages, threadTs }),
    );
    window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/chat/`;
  };

  return (
    <div className="p-baseChat">
      {isOpen ? (
        <div className="p-baseChat__board">
          <div className="p-chatPanelSmall__head">
            <h1 className="p-chatPanelSmall__title">
              <span className="icon-base icon-sec-chat icon-size36 icon-color-darkGreen " />
              その他のセキュリティ相談
            </h1>
            <p className="p-chatPanelSmall__text">
              貴方がセキュリティで懸念されている点を入力してください。AIによる24時間自動応答でお答えいたします。
            </p>
            <div className="p-chatPanelSmall__action">
              <Button onClick={() => handleMaximized()} variant="text">
                <span className="icon-base icon-sec-expansion icon-size20 icon-color-darkGreen" />
              </Button>
              <Button onClick={() => setIsOpen(false)} variant="text">
                <span className="icon-base icon-sec-close icon-size20 icon-color-darkGreen" />
              </Button>
            </div>
          </div>
          <div className="p-chatPanelSmall__body">
            <MainContainer>
              <ChatContainer>
                <MessageList>
                  {messages.map((m, idx) => (
                    <div
                      key={idx}
                      className={
                        m.role === 'user'
                          ? 'p-chatMs p-chatMs--right'
                          : 'p-chatMs'
                      }
                    >
                      {m.role !== 'user' && (
                        <span className="icon-base icon-sec-operator icon-size36 icon-color-darkGreen" />
                      )}
                      <div className="p-chatMs__message">
                        <Message
                          model={{
                            direction:
                              m.role === 'user' ? 'outgoing' : 'incoming',
                          }}
                        >
                          <Message.CustomContent>
                            <ChatMessageBubbleMarkDown content={m.content} />
                          </Message.CustomContent>
                        </Message>
                        {m.role !== 'user' && (
                          <>
                            {m.feedback !== undefined
                              && m.replyNumber !== undefined && (
                              <div className="p-chatMs__feedback">
                                <Button
                                  variant="text"
                                  className={m.feedback}
                                  onClick={() =>
                                    handleFeedback(m.replyNumber, 'good')}
                                >
                                  <span
                                    className={
                                      m.feedback === 'good'
                                        ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen'
                                        : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen'
                                    }
                                  />
                                </Button>
                                <Button
                                  variant="text"
                                  className={m.feedback}
                                  onClick={() =>
                                    handleFeedback(m.replyNumber, 'bad')}
                                >
                                  <span
                                    className={
                                      m.feedback === 'bad'
                                        ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen icon-rotate180'
                                        : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen icon-rotate180'
                                    }
                                  />
                                </Button>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                  {/* 待機中のローダー */}
                  {isSending && (
                    <div className="p-chatPanelSmall__loading">
                      <div className="c-loaderChat">
                        <span />
                        <span />
                        <span />
                      </div>
                    </div>
                  )}
                </MessageList>
                <MessageInput
                  placeholder="メッセージを入力してください"
                  onSend={handleSend}
                  onChange={handleInputChange}
                  value={inputText}
                />
              </ChatContainer>
            </MainContainer>
            <div className="p-chatPanelSmall__footer">
              {chatError && (
                <p className="p-chatPanelSmall__error">
                  <span className="icon-base icon-sec-caution icon-color-error" />
                  {chatError}
                </p>
              )}
              <p className="p-chatPanelSmall__note">
                回答は全てAIによる自動生成です
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="p-baseChat__button">
          <Button
            id="chat_common"
            onClick={() => setIsOpen(true)}
            variant="chat"
          >
            <span className="icon-base icon-sec-chat icon-size36 icon-color-darkGreen" />
            その他のセキュリティ相談
          </Button>
        </div>
      )}
    </div>
  );
};

export default ChatButton;
