import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontaine<PERSON>,
  MessageList,
  Message,
  MessageInput,
} from '@chatscope/chat-ui-kit-react';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import Button from '../../common/components/Button';
import useChatHandler from '../../common/hooks/useChatHandler';
import { ChatMessageBubbleMarkDown } from './ChatMessageBubbleMarkDown';

function Chat() {
  const [messages, setMessages] = useState([]);
  const [threadTs, setThreadTs] = useState(null);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isSending, setIsSending] = useState(false);
  const [inputText, setInputText] = useState('');

  const location = useLocation();
  const {
    isChatValidAndAllowed,
    incrementChatCounter,
    clearChatErrors,
    chatError,
  } = useChatHandler();

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!grecaptcha) return;
    try {
      const state = localStorage.getItem('state');
      if (!state) {
        setMessages([
          {
            role: 'system',
            content:
              'セキュリティで懸念されている点について、お気軽にお尋ねください。',
          },
        ]);
        return;
      }

      const { content, messages, threadTs } = JSON.parse(state);
      localStorage.removeItem('state');

      if (messages && threadTs) {
        setMessages(messages);
        setThreadTs(threadTs);
        return;
      }

      if (content) {
        handleSend(null, content);
        return;
      }

      setMessages([
        {
          role: 'system',
          content:
            'セキュリティで懸念されている点について、お気軽にお尋ねください。',
        },
      ]);
      return;
    } catch (err) {
      console.error(err);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [grecaptcha]);

  const handleInputChange = (_, textContent) => {
    clearChatErrors();
    setInputText(textContent);
  };

  const handleSend = async (_, post) => {
    if (!isChatValidAndAllowed(post)) {
      return;
    }

    const oldMessages = messages;
    setMessages([...oldMessages, { role: 'user', content: post }]);
    setInputText('');
    setIsSending(true);
    incrementChatCounter();
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/chat`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            post,
            messages: oldMessages
              .filter(({ role }) => ['user', 'assistant'].includes(role))
              .map(m => ({ role: m.role, content: m.content })),
            threadTs,
            recaptchaToken,
            path: location.pathname,
          }),
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let hasNewLineOccurred = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        if (!hasNewLineOccurred && buffer.includes('\n')) {
          const [messageBeforeNewLine, ...rest] = buffer.split('\n');
          setThreadTs(messageBeforeNewLine);
          buffer = rest.join('\n');
          hasNewLineOccurred = true;
        }
        if (hasNewLineOccurred && buffer.length > 0) {
          setMessages([
            ...oldMessages,
            { role: 'user', content: post },
            { role: 'assistant', content: buffer },
          ]);
          setIsSending(false);
        }
      }
      setMessages([
        ...oldMessages,
        { role: 'user', content: post },
        {
          role: 'assistant',
          content: buffer,
          feedback: null,
          replyNumber: oldMessages.filter(({ role }) => role === 'assistant')
            .length,
        },
      ]);
      reader.releaseLock();
    } catch (err) {
      console.error(err);
    } finally {
      setIsSending(false);
    }
  };

  const handleFeedback = async (replyNumber, feedback) => {
    try {
      let newFeedback = feedback;
      const newMessages = messages.map((m) => {
        if (m.role !== 'assistant') return m;
        if (m.replyNumber !== replyNumber) return m;

        if (m.feedback === feedback) {
          newFeedback = null;
        }
        m.feedback = newFeedback;
        return m;
      });
      if (newFeedback === 'bad') {
        if (
          newMessages.length > 0
          && newMessages.slice(-1)[0].role !== 'system'
        ) {
          newMessages.push({
            role: 'system',
            content:
              'ご期待にお応えできず申し訳ございません。よろしければさらに詳しくお聞かせいただけませんでしょうか。',
          });
        }
      }
      setMessages(newMessages);

      if (!threadTs) return;
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/feedback`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            threadTs,
            replyNumber,
            feedback: newFeedback,
            recaptchaToken,
          }),
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <section className="p-base p-chat">
      <div className="p-base__inner">
        <div className="p-chatPanel">
          <div className="p-chatPanel__body">
            <MainContainer>
              <ChatContainer>
                <MessageList>
                  <div className="p-chatPanel__head">
                    <h1 className="p-chatPanel__title">
                      <span className="icon-base icon-sec-chat icon-size40 icon-color-darkGreen" />
                      その他のセキュリティ相談
                    </h1>
                    <p className="p-chatPanel__text">
                      貴方がセキュリティで懸念されている点を入力してください。AIによる24時間自動応答でお答えいたします。
                    </p>
                  </div>
                  {messages.map((m, idx) => (
                    <div
                      key={idx}
                      className={
                        m.role === 'user'
                          ? 'p-chatMs p-chatMs--right'
                          : 'p-chatMs'
                      }
                    >
                      {m.role !== 'user' && (
                        <span className="icon-base icon-sec-operator icon-size36 icon-color-darkGreen" />
                      )}
                      <div className="p-chatMs__message">
                        <Message
                          model={{
                            direction:
                              m.role === 'user' ? 'outgoing' : 'incoming',
                          }}
                        >
                          <Message.CustomContent>
                            <ChatMessageBubbleMarkDown content={m.content} />
                          </Message.CustomContent>
                        </Message>
                        {m.role !== 'user' && (
                          <>
                            {m.feedback !== undefined
                              && m.replyNumber !== undefined && (
                              <div className="p-chatMs__feedback">
                                <Button
                                  variant="text"
                                  className={m.feedback}
                                  onClick={() =>
                                    handleFeedback(m.replyNumber, 'good')}
                                >
                                  <span
                                    className={
                                      m.feedback === 'good'
                                        ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen'
                                        : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen'
                                    }
                                  />
                                </Button>
                                <Button
                                  variant="text"
                                  className={m.feedback}
                                  onClick={() =>
                                    handleFeedback(m.replyNumber, 'bad')}
                                >
                                  <span
                                    className={
                                      m.feedback === 'bad'
                                        ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen icon-rotate180'
                                        : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen icon-rotate180'
                                    }
                                  />
                                </Button>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                  {/* 待機中のローダー */}
                  {isSending && (
                    <div className="p-chatPanel__loading">
                      <div className="c-loaderChat">
                        <span />
                        <span />
                        <span />
                      </div>
                    </div>
                  )}
                </MessageList>
                <MessageInput
                  placeholder="メッセージを入力してください"
                  onSend={handleSend}
                  onChange={handleInputChange}
                  value={inputText}
                />
              </ChatContainer>
            </MainContainer>
            <div className="p-chatPanel__footer">
              {chatError && (
                <p className="p-chatPanel__error">
                  <span className="icon-base icon-sec-caution icon-color-error" />
                  {chatError}
                </p>
              )}
              <p className="p-chatPanel__note">
                回答は全てAIによる自動生成です
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Chat;
