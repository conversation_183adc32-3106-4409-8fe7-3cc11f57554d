import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import Button from '../../../common/components/Button';
import { validateYourbrandContactForm } from '../../../common/validators';

function YourbrandContactForm({ tld, beforeHandleSubmit, afterHandleSubmit }) {
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [form, setForm] = useState({
    company: '',
    lastName: '',
    firstName: '',
    email: '',
    telephone: '',
  });
  const [errors, setErrors] = useState({});
  const { lastName, firstName, email, telephone, company } = form;

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const validateForm = () => {
    const result = validateYourbrandContactForm.validate(form);

    if (!result.isValid) {
      const newErrors = {};

      if (result.errors) {
        result.errors.forEach((error) => {
          const field = error.instancePath.replace('/', '');
          if (field && error.message) {
            newErrors[field] = error.message;
          }
        });
      }

      setErrors(newErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsProcessing(true);

      if (beforeHandleSubmit) {
        beforeHandleSubmit();
      }

      try {
        if (!grecaptcha) {
          throw new Error('reCAPTCHA not loaded');
        }

        const recaptchaToken = await grecaptcha.execute(
          import.meta.env.VITE_RECAPTCHA_SITE_KEY,
          { action: 'submit' },
        );

        const response = await fetch(`${import.meta.env.VITE_API_HOST}/api/contact`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            targets: ['brand_tld'],
            fullname: `${lastName} ${firstName}`,
            email,
            telephone,
            recaptchaToken,
            company,
            tld,
          }),
        });

        const data = await response.json();

        if (data.status === 'success') {
          setIsProcessing(false);

          setForm({
            lastName: '',
            firstName: '',
            email: '',
            telephone: '',
          });
          setErrors({});
        } else {
          setIsProcessing(false);
          setErrors({ submit: 'エラーが発生しました。もう一度お試しください。' });
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        setIsProcessing(false);
        setErrors({ submit: 'エラーが発生しました。もう一度お試しください。' });
      } finally {
        if (afterHandleSubmit) {
          afterHandleSubmit();
        }
      }
    }
  };

  return (
    <div className="c-modalBox">
      <h2 className="c-modalBox__title">.貴社名の資料請求・ご相談（無料）</h2>
      <div className="c-modalBox__form">
        <form onSubmit={handleSubmit} className="c-inputForm">
          <div className="c-inputForm__box">
            <label htmlFor="company" className="c-input__label">
              会社名
              <span className="c-input__required">※</span>
            </label>
            <input
              type="text"
              id="company"
              name="company"
              placeholder="会社名を入力してください"
              value={company}
              onChange={handleChange}
              className="c-input__input"
              required
            />
            {errors.company && (
              <div className="c-input__error">{errors.company}</div>
            )}
          </div>
          <div className="c-inputForm__column">
            <div className="c-inputForm__columnBox">
              <label htmlFor="lastName" className="c-input__label">
                姓
                <span className="c-input__required">※</span>
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                placeholder="安全"
                value={lastName}
                onChange={handleChange}
                className="c-input__input"
                required
              />
              {errors.lastName && (
                <div className="c-input__error">{errors.lastName}</div>
              )}
            </div>
            <div className="c-inputForm__columnBox">
              <label htmlFor="firstName" className="c-input__label">
                名
                <span className="c-input__required">※</span>
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                placeholder="守"
                value={firstName}
                onChange={handleChange}
                className="c-input__input"
                required
              />
              {errors.firstName && (
                <div className="c-input__error">{errors.firstName}</div>
              )}
            </div>
          </div>
          <div className="c-inputForm__box">
            <label htmlFor="email" className="c-input__label">
              メールアドレス
              <span className="c-input__required">※</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="メールアドレスを入力してください"
              value={email}
              onChange={handleChange}
              className="c-input__input"
              required
            />
            {errors.email && (
              <div className="c-input__error">{errors.email}</div>
            )}
          </div>
          <div className="c-inputForm__box">
            <label htmlFor="phone" className="c-input__label">
              電話番号
              <span className="c-input__required">※</span>
            </label>
            <input
              id="phone"
              name="telephone"
              type="tel"
              placeholder="電話番号を入力してください"
              className="c-input__input"
              value={telephone}
              onChange={handleChange}
              required
            />
            <div className="c-input__error">{errors.telephone}</div>
          </div>
          <div className="c-inputForm__button">
            <Button
              type="submit"
              variant="brandTldPrimary"
              widthSize="full"
              isLoading={isProcessing}
            >
              相談する
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

YourbrandContactForm.propTypes = {
  tld: PropTypes.string,
  beforeHandleSubmit: PropTypes.func,
  afterHandleSubmit: PropTypes.func,
};

export default YourbrandContactForm;
