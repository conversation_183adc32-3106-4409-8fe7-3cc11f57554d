import { useState, useEffect } from 'react';
import mailImage from '../../assets/img/illust_mail.png';
import Button from '../../common/components/Button';
import ErrorComponent from '../../common/components/Error';
import useModal from '../../common/hooks/useModal';
import { ErrorMessages } from '../../common/messages/error';
import YourbrandContactForm from './form/YourbrandContactForm';

function YourbrandSearch() {
  const [isLoading, setIsLoading] = useState(true);
  const [available, setAvailable] = useState(false);
  const [error, setError] = useState('');
  const { show, Modal, setIsModalClosable } = useModal();
  const [modalContent, setModalContent] = useState(<></>);
  const MAIL_IMAGE = mailImage;

  const queryParams = new URLSearchParams(window.location.search);
  const tld = queryParams.get('tld');

  useEffect(() => {
    if (!tld) {
      setError(ErrorMessages.NOT_FOUND);
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(`${import.meta.env.VITE_API_HOST}/api/tld?name=${encodeURIComponent(tld)}`);
        if (!response.ok) throw new Error('API request failed');
        const { result } = await response.json();

        setAvailable(result.available);
      } catch (err) {
        console.error(err);
        setError(ErrorMessages.NOT_FOUND);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tld]);

  const beforeHandleSubmit = () => {
    setIsModalClosable(false);
  };

  const afterHandleSubmit = () => {
    const completeModalContent = (
      <div className="c-modalBox">
        <div className="c-modalBox__img">
          <img src={MAIL_IMAGE} alt="" />
        </div>
        <h2 className="c-modalBox__title">資料請求・ご相談を受付しました</h2>
        <p className="c-modalBox__text">メールアドレスにご案内をお送りいたします。</p>
      </div>
    );
    setModalContent(completeModalContent);
    setIsModalClosable(true);
  };

  const handleButtonClick = () => {
    setModalContent(
      <YourbrandContactForm
        beforeHandleSubmit={beforeHandleSubmit}
        afterHandleSubmit={afterHandleSubmit}
        tld={tld}
      />,
    );
    show();
  };

  if (isLoading) {
    return <></>;
  }

  if (error) {
    return <ErrorComponent text={error} />;
  }

  return (
    <div className="p-yourbrand">
      <section className="p-yourbrand__sec p-yourbrand__sec--gray">
        <div className="p-yourbrand__inner p-yourbrand__inner--top">
          <h1 className="p-yourbrand__title">
            「.貴社名」
            <span className="p-yourbrand__titleBreak">検索結果</span>
          </h1>
          <div className="p-yourbrand__result">
            <div className="c-panel">
              <p className={`c-panel__note c-panel__note--${available ? 'success' : 'warning'}`}>
                {available
                  ? '申請可能です'
                  : '申請できません'}
              </p>
              <div className="c-panel__result">
                {available
                  ? (
                    <>
                      入力された名称は、.貴社名（ブランドTLD）として申請可能です。
                      <br />
                      申請には約一年を要しますので、早めのご準備をおすすめします。
                    </>
                  )
                  : (
                    <>
                      入力された名称は、すでに他の組織により登録がされています。
                      <br />
                      代替案や類似名称での申請可否をご希望の場合は、お気軽にお問合せください。
                    </>
                  )}
              </div>
              <div className="c-panelBox">
                <dl className="c-panelBox__item">
                  <dt>入力した名称</dt>
                  <dd>{`.${tld}`}</dd>
                </dl>
                <div className="c-panelBox__button">
                  <Button
                    id="another_brand"
                    as="a"
                    href="/security/yourbrand/"
                    target="_blank"
                    rel="noopener"
                    external
                    exIcon="small"
                    variant="secondary"
                  >
                    他の名称で検索
                  </Button>
                </div>
              </div>
              {!available
                && (
                  <div className="c-panelBox c-panelBox--border">
                    <dl className="c-panelBox__item">
                      <dt>登録済み</dt>
                      <dd>{`.${tld}`}</dd>
                    </dl>
                  </div>
                )}
              <p className="c-panel__annotation c-panel__annotation--center c-panel__annotation--light">
                ※.貴社名（ブランドTLD）は、ICANNの審査を通じて登録が確定します。検索結果は、.貴社名の登録を保証するものではございませんのであらかじめご了承ください。
              </p>
            </div>
          </div>
          <div className="p-yourbrand__button">
            <Button id="contact_yourbrand_search" onClick={handleButtonClick} variant="brandTldAccent" widthSize="full">
              資料請求・ご相談（無料）
            </Button>
            <div className="p-yourbrand__note">※申請には約一年を要します。早めのご準備をおすすめします</div>
          </div>
        </div>
      </section>
      <section className="p-yourbrand__sec gtm-view" id="section_yourbrand_flow">
        <div className="p-yourbrand__inner">
          <h2 className="p-yourbrand__title">
            「.貴社名」
            <span className="p-yourbrand__titleBreak">申請の流れ</span>
          </h2>
          <div className="p-yourbrandFlow">
            <div className="p-yourbrandFlow__list">
              <div className="p-yourbrandFlow__timeline">
                申請
                <span>約１年</span>
              </div>
              <div className="p-yourbrandFlow__cardWrap">
                <div className="p-yourbrandFlow__card">
                  <span className="p-yourbrandFlow__num">1</span>
                  <div className="p-yourbrandFlow__content">
                    <p className="p-yourbrandFlow__heading">「.貴社名」申請</p>
                  </div>
                </div>
                <div className="p-yourbrandFlow__card">
                  <span className="p-yourbrandFlow__num">2</span>
                  <div className="p-yourbrandFlow__content">
                    <p className="p-yourbrandFlow__heading">ICANN審査</p>
                    <p className="p-yourbrandFlow__body">
                      審査内容：1.「.貴社名（ブランドTLD）」の申請目的&nbsp;&nbsp;2. 財務要件&nbsp;&nbsp;3. システム要件
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-yourbrandFlow__list">
              <div />
              <div className="p-yourbrandFlow__card">
                <span className="p-yourbrandFlow__num">3</span>
                <div className="p-yourbrandFlow__content">
                  <p className="p-yourbrandFlow__heading">システム設定</p>
                </div>
              </div>
            </div>
            <div className="p-yourbrandFlow__list">
              <div className="p-yourbrandFlow__timeline">
                運用
              </div>
              <div className="p-yourbrandFlow__card">
                <span className="p-yourbrandFlow__num">4</span>
                <div className="p-yourbrandFlow__content">
                  <p className="p-yourbrandFlow__heading">「.貴社名」運用開始</p>
                </div>
              </div>
            </div>
          </div>
          <div className="p-yourbrand__button">
            <Button id="contact_yourbrand_flow" onClick={handleButtonClick} variant="brandTldAccent" widthSize="full">
              資料請求・ご相談（無料）
            </Button>
            <div className="p-yourbrand__note">※申請には約一年を要します。早めのご準備をおすすめします</div>
          </div>
        </div>
      </section>
      <Modal>
        {modalContent}
      </Modal>
    </div>
  );
}

export default YourbrandSearch;
