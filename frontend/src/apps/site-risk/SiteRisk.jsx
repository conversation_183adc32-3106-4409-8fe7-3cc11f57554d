import { useState, useEffect } from 'react';
import mailImage from '../../assets/img/illust_mail.png';
import errorImage from '../../assets/img/rank_alert.png';
import ErrorComponent from '../../common/components/Error';
import useModal from '../../common/hooks/useModal';
import { ErrorMessages } from '../../common/messages/error';
import ChatButton from '../chat/ChatButton';
import SslContactForm from './form/SslContactForm';
import ExpiredOverview from './overview/ExpiredOverview';
import OverView from './overview/Overview';
import Result from './result/Result';

const MAIL_IMAGE = mailImage;
const ERROR_IMAGE = errorImage;

const MODAL_TITLES = {
  nds: '無料見積を受け付けいたしました',
  cloud: '無料お試しを受け付けいたしました',
  ssl: '無料お試しを受け付けいたしました',
  impersonation: '無料見積を受け付けいたしました',
};

function SiteRisk() {
  const [isLoading, setIsLoading] = useState(true);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [modalChildren, setModalChildren] = useState(<></>);
  const [isProcessing, setIsProcessing] = useState(false);

  const { show, Modal } = useModal();

  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get('code');

  const isExpired
    = !result?.nds && !result?.cloud && !result?.ssl && !result?.impersonation;

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!code) {
      setError(ErrorMessages.NOT_FOUND);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    (async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_API_HOST}/api/site-risk?code=${code}`,
        );

        if (response.ok) {
          const { result, status } = await response.json();
          if (status === 'success') {
            setResult(result);
          }
        } else {
          const { status, message } = await response.json();
          if (status === 'error') {
            throw new Error(message);
          }
        }
      } catch (err) {
        if (err.message === 'Code is expired') {
          setError(ErrorMessages.CODE_EXPIRED);
          return;
        }
        if (err.message === 'Invalid code') {
          setError(ErrorMessages.INVALID_CODE);
          return;
        }
        console.error(err);
        setError(ErrorMessages.NOT_FOUND);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [code]);

  const handleContactSubmit = async ({ target, telephone, fullname }) => {
    if (target === 'ssl') {
      // setIsModalClosable(false);
      // setIsSslProcessing(true);
    } else {
      setIsProcessing(true);
    }

    let image, title, message;
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }

      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );

      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/contact`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            code,
            email: result.overview?.email,
            targets: [target],
            recaptchaToken,
            threadTs: result.overview?.threadTs,
            fullname,
            telephone,
          }),
        },
      );

      if (response.ok) {
        image = MAIL_IMAGE;
        title = MODAL_TITLES[target];
        message = 'メールアドレスにご案内をお送りいたします。';
      } else {
        const { message } = await response.json();
        throw new Error(`Contact api error message: ${message}`);
      }
    } catch (err) {
      image = ERROR_IMAGE;
      title = 'エラーが発生しました。';
      message = '再度お試しください。';

      console.error(err);
    } finally {
      if (target === 'ssl') {
        // setIsSslProcessing(false);
        // setIsModalClosable(true);
      } else {
        setIsProcessing(false);
      }

      const modalContent = (
        <div className="c-modalBox">
          <div className="c-modalBox__img">
            <img src={image} alt="" />
          </div>
          <h2 className="c-modalBox__title">{title}</h2>
          <p className="c-modalBox__text">{message}</p>
        </div>
      );
      setModalChildren(modalContent);
      show();
    }
  };

  const onClickedContact = (target) => {
    if (target === 'ssl') {
      const modalContent = <SslContactForm onSubmit={handleContactSubmit} />;
      setModalChildren(modalContent);
      show();
    } else {
      handleContactSubmit({ target });
    }
  };

  if (isLoading) {
    return <></>;
  }

  if (error) {
    return <ErrorComponent text={error} />;
  }
  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <div className="p-base__message">
            <div className="c-glassPanel">
              <p className="c-glassPanel__text">
                Webサイトリスク診断日：
                {new Date(result.overview.createdAt).toLocaleDateString(
                  'ja-JP',
                )}
              </p>
            </div>
          </div>
          {isExpired
            ? (
              <ExpiredOverview {...result.overview} />
            )
            : (
              <OverView {...result.overview} code={code} result={result} />
            )}
          {!isExpired && (
            <>
              <Modal>{modalChildren}</Modal>
              <Result
                nds={result.nds}
                cloud={result.cloud}
                ssl={result.ssl}
                impersonation={result.impersonation}
                onClickedContact={onClickedContact}
                isProcessing={isProcessing}
              />
            </>
          )}
        </div>
      </div>
      <div className="p-baseFixed">
        <div className="p-baseFixed__chatButton">
          <ChatButton />
        </div>
      </div>
    </section>
  );
}

export default SiteRisk;
