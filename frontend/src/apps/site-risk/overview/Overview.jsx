import PropTypes from 'prop-types';
import { useState } from 'react';
import Button from '../../../common/components/Button';
import SiteRiskPeriodicCheckup from '../SiteRiskPeriodicCheckup';
import OverviewCard from './OverviewCard';
import OverviewGrid from './OverviewGrid';

const NDS_RANKS = {
  A: '安全です',
  B: '安全です',
  C: '要対策',
  D: '要対策',
  E: '要緊急対応',
};

const IMPERSONATION_RANKS = {
  A: '安全です',
  B: '要注意',
  C: '危険',
  D: '非常に危険',
};

const STATUSES = {
  alert: 1,
  warning: 2,
  safe: 3,
};

const NdsIcon = () => (
  <span className="icon-base icon-sec-security icon-size20 icon-color-darkGreen" />
);
const CloudIcon = () => (
  <span className="icon-base icon-sec-cloud icon-size20 icon-color-darkGreen" />
);
const SslIcon = () => (
  <span className="icon-base icon-sec-ssl icon-size20 icon-color-darkGreen" />
);
const ImpersonationIcon = () => (
  <span className="icon-base icon-sec-impersonation icon-size20 icon-color-darkGreen" />
);

function Overview({ fqdn, nds, cloud, ssl, impersonation, code, result }) {
  const [currentSectionIdx, setCurrentSectionIdx] = useState(0);

  let ndsStatus = 'safe';
  switch (nds.rank) {
    case 'A':
      ndsStatus = 'safe';
      break;
    case 'B':
      ndsStatus = 'safe';
      break;
    case 'C':
      ndsStatus = 'warning';
      break;
    case 'D':
      ndsStatus = 'warning';
      break;
    case 'E':
      ndsStatus = 'alert';
      break;
    default:
      ndsStatus = 'safe';
      break;
  }

  let sslStatus = ssl.status;

  let impersonationStatus = 'safe';
  switch (impersonation.rank) {
    case 'A':
      impersonationStatus = 'safe';
      break;
    case 'B':
      impersonationStatus = 'warning';
      break;
    case 'C':
      impersonationStatus = 'alert';
      break;
    case 'D':
      impersonationStatus = 'alert';
      break;
    default:
      impersonationStatus = 'safe';
      break;
  }

  const worstStatus = [ndsStatus, sslStatus, impersonationStatus].sort(
    (a, b) => STATUSES[a] - STATUSES[b],
  )[0];

  const sections = [
    {
      id: 'anchorLinker_nds',
      targetId: 'anchorLink_nds',
      title: (
        <span>
          Webサイト
          <span>脆弱性診断</span>
        </span>
      ),
      icon: NdsIcon,
      status: nds.status,
      rank: nds.rank,
      type: 'nds',
      text: NDS_RANKS[nds.rank],
    },
    {
      id: 'anchorLinker_cloud',
      targetId: 'anchorLink_cloud',
      title: (
        <span>
          クラウド利用・
          <span>リスク診断</span>
        </span>
      ),
      icon: CloudIcon,
      status: cloud.status,
      type: 'cloud',
      text: cloud.text,
    },
    {
      id: 'anchorLinker_ssl',
      targetId: 'anchorLink_ssl',
      title: (
        <span>
          実在証明・盗聴防止
          <span>（SSL）診断</span>
        </span>
      ),
      icon: SslIcon,
      status: ssl.status,
      type: 'ssl',
      text: ssl.text,
    },
    {
      id: 'anchorLinker_impersonation',
      targetId: 'anchorLink_impersonation',
      title: 'なりすまし診断',
      icon: ImpersonationIcon,
      status: impersonation.status,
      rank: impersonation.rank,
      type: 'impersonation',
      text: IMPERSONATION_RANKS[impersonation.rank],
    },
  ];

  const handleNewDiagnostic = () => {
    window.open(`${import.meta.env.VITE_PATH_PREFIX}/`, '_blank');
  };

  return (
    <div className="c-overview">
      {worstStatus === 'alert'
        ? (
          <div className="c-overview__note c-overview__note--error">
            <span className="icon-base icon-sec-caution icon-color-white" />
            早急な対応が必要な項目があります
          </div>
        )
        : worstStatus === 'warning'
          ? (
            <div className="c-overview__note c-overview__note--warning">
              概ね安全ですが、引き続き注意を要する状態です
            </div>
          )
          : (
            <div className="c-overview__note c-overview__note--safe">
              すべての診断項目が安全な状態です
            </div>
          )}
      <div className="c-overview__head">
        <dl>
          <dt>診断対象のURL</dt>
          <dd>{fqdn}</dd>
        </dl>
        <div className="c-overview__button">
          <Button
            id="another_url"
            onClick={handleNewDiagnostic}
            external
            exIcon="xsmall"
            variant="textUnderLine"
          >
            他のURLを診断
          </Button>
        </div>
      </div>
      <div className="c-overview__check">
        <SiteRiskPeriodicCheckup
          code={code}
          nextCheckedAt={result.configuration?.nextCheckedAt}
          isRegularly={result.configuration?.isRegularly}
          interval={result.configuration?.interval}
          isNotification={result.configuration?.isNotification}
          createdAt={result.overview.createdAt}
        />
      </div>
      <div className="c-overview__nav">
        <OverviewGrid>
          {sections.map((section, idx) => (
            <OverviewCard
              key={section.id}
              {...section}
              className={`c-overviewCard c-overviewCard--tab c-tab__btn ${idx === currentSectionIdx ? 'c-tab__btn--active' : ''}`}
              onClick={() => {
                const element = document.getElementById(section.targetId);
                if (element) {
                  if (currentSectionIdx !== idx) {
                    const currentTarget = document.getElementById(
                      sections[currentSectionIdx].targetId,
                    );
                    currentTarget.classList.remove('c-tab__content--active');

                    element.classList.add('c-tab__content--active');
                    setCurrentSectionIdx(idx);
                  }
                }
              }}
            />
          ))}
        </OverviewGrid>
      </div>
    </div>
  );
}

Overview.propTypes = {
  fqdn: PropTypes.string.isRequired,
  nds: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  cloud: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  ssl: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  impersonation: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  code: PropTypes.string.isRequired,
  result: PropTypes.shape({
    configuration: PropTypes.shape({
      nextCheckedAt: PropTypes.string,
      isRegularly: PropTypes.bool,
      interval: PropTypes.number,
      isNotification: PropTypes.bool,
    }),
    overview: PropTypes.shape({ createdAt: PropTypes.string }).isRequired,
  }).isRequired,
};

export default Overview;
