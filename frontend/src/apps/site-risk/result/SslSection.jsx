import PropTypes from 'prop-types';
import rankAlert from '../../../assets/img/rank_alert.png';
import rankSafe from '../../../assets/img/rank_safe.png';
import rankWarning from '../../../assets/img/rank_warning.png';
import Button from '../../../common/components/Button';
import { calculateRemainder, formatDatetime } from '../../../common/utils';
import Badge from './Badge';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const STATUS_IMAGES = {
  safe: rankSafe,
  warning: rankWarning,
  alert: rankAlert,
};

const STATUSES = { safe: '低リスク', warning: '中リスク', alert: '緊急' };

function SslSection({ ssl, onClickedContact }) {
  if (!ssl) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
            実在証明・盗聴防止（SSL）診断
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (ssl.status && ssl.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
            実在証明・盗聴防止（SSL）診断
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  const now = new Date();
  const remainingDays = Math.floor(
    (new Date(ssl.details[0].expires) - now) / (24 * 60 * 60 * 1000),
  );

  let status = 'safe';
  let message = 'ご利用中のSSLは有効期限が十分に残っております。';
  let note = '安心です';

  let component;

  if (!ssl.summary.hostnameVerification) {
    status = 'alert';
    message
      = '使用中の証明書がドメインと一致していません。早急に設定を見直してください。';
    note = '不一致';
    component = (
      <div className="p-siteRiskDetail">
        ドメインと異なる証明書
        <div className="p-siteRiskDetail__badge">
          <Badge status="alert" message={STATUSES['alert']} />
        </div>
      </div>
    );
  } else if (!ssl.summary.certVerification) {
    status = 'alert';
    message
      = '証明書が無効になっています。早急に再設定または更新を行ってください。';
    note = '無効';
    component = (
      <div className="p-siteRiskDetail">
        無効な証明書
        <div className="p-siteRiskDetail__badge">
          <Badge status="alert" message={STATUSES['alert']} />
        </div>
      </div>
    );
  } else {
    const after30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    if (ssl.summary.freeSslProvider) {
      window.ssl = { isFree: true };
      if (new Date(ssl.summary.earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        message
          = '信頼性の低い無料SSLをご利用中です。変更を推奨します（フィッシングサイトの95%以上は無料SSLを利用しています）。';
        note = '変更推奨';
      } else if (
        new Date(ssl.summary.earliestExpires).getTime() < after30Days.getTime()
      ) {
        status = 'warning';
        message
          = '信頼性の低い無料SSLをご利用中です。変更を推奨します（フィッシングサイトの95%以上は無料SSLを利用しています）。';
        note = '変更推奨';
      } else {
        status = 'warning';
        message
          = '信頼性の低い無料SSLをご利用中です。変更を推奨します（フィッシングサイトの95%以上は無料SSLを利用しています）。';
        note = '変更推奨';
      }
    } else {
      if (new Date(ssl.summary.earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        message = '今すぐSSLを更新してください。';
        note = '変更推奨';
      } else if (
        new Date(ssl.summary.earliestExpires).getTime() < after30Days.getTime()
      ) {
        status = 'warning';
        message = '早急に有効期限をご確認ください。';
        note = '変更推奨';
      } else {
        status = 'safe';
        message = 'ご利用中のSSLは有効期限が十分に残っております。';
        note = '安全です';
      }
    }
    component = (
      <div className="p-siteRiskDetail">
        {remainingDays >= 0 && (
          <p className="p-siteRiskDetail__text">
            有効期限：
            <span>
              {ssl.details[0].expires.split('T')[0]}
              {' '}
              まで
              {`（${calculateRemainder(new Date(ssl.details[0].expires))}）`}
            </span>
          </p>
        )}
        {remainingDays < 0 && (
          <p className="p-siteRiskDetail__text">
            有効期限：
            <span className="p-siteRiskDetail__alert">
              {ssl.details[0].expires.split('T')[0]}
              {' '}
              まで（期限切れ）
            </span>
          </p>
        )}
        <div className="p-siteRiskDetail__badge">
          <Badge status={status} message={STATUSES[status]} />
        </div>
      </div>
    );
  }

  const isVisibleContactButton = !(
    ssl.summary.certVerification
    && ssl.summary.hostnameVerification
    && !ssl.summary.freeSslProvider
  );

  return (
    <section>
      <div id="section_ssl" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
          実在証明・盗聴防止（SSL）診断
        </h2>
        <div className="c-panel__content c-panel__content--gap20">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              <img src={STATUS_IMAGES[status]} alt="" />
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              {isVisibleContactButton && (
                <Button
                  id="contact_ssl"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                >
                  今すぐwebを安全に
                  <br />
                  テストSSL（無料）
                </Button>
              )}
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            <p className={`c-panel__note c-panel__note--${status}`}>{note}</p>
            <p className="c-panel__result">{message}</p>
            {ssl.details.length === 0 && (
              <div className="c-panelList">
                <p className="c-panelList__note">
                  <span className="icon-base icon-sec-attention icon-size16 icon-color-darkGreen" />
                  SSL証明書が見つかりませんでした
                </p>
              </div>
            )}
            <ul className="c-panelList">
              {ssl.summary.freeSslProvider && (
                <li>
                  <div className="p-siteRiskDetail">
                    {ssl.summary.freeSslProvider}
                    （無料SSL）を利用中
                  </div>
                </li>
              )}
              <li>{component}</li>
            </ul>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
              {isVisibleContactButton && (
                <Button
                  id="contact_ssl_sp"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                >
                  今すぐwebを安全に
                  <br />
                  テストSSL（無料）
                </Button>
              )}
            </div>
            {/* spのみ表示 end */}
          </div>
        </div>
        {ssl.summary.createdAt && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            本診断結果は、
            {' '}
            {formatDatetime(ssl.summary.createdAt)}
            時点のものです。診断結果は、
            <Button
              as="a"
              href="https://www.gmo.jp/security/check/agreement/"
              target="_blank"
              rel="noopener"
              variant="textXs"
              referrerPolicy="strict-origin-when-cross-origin"
            >
              利用規約
            </Button>
            をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。
          </p>
        )}
      </div>
    </section>
  );
}

SslSection.propTypes = {
  ssl: PropTypes.shape({
    status: PropTypes.string,
    details: PropTypes.arrayOf(PropTypes.shape({ expires: PropTypes.string })),
    summary: PropTypes.shape({
      hostnameVerification: PropTypes.bool,
      certVerification: PropTypes.bool,
      freeSslProvider: PropTypes.string,
      earliestExpires: PropTypes.string,
      createdAt: PropTypes.string,
    }),
  }),
  onClickedContact: PropTypes.func.isRequired,
};

export default SslSection;
