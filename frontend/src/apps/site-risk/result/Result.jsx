import PropTypes from 'prop-types';
import CloudSection from './CloudSection';
import ImpersonationSection from './ImpersonationSection';
import NdsSection from './NdsSection';
import SslSection from './SslSection';

function Result({
  nds,
  cloud,
  ssl,
  impersonation,
  onClickedContact,
  isProcessing,
}) {
  return (
    <>
      <div
        id="anchorLink_nds"
        className="c-tab__content c-tab__content--active"
      >
        <NdsSection
          nds={nds}
          onClickedContact={() => onClickedContact('nds')}
          isProcessing={isProcessing}
        />
      </div>
      <div id="anchorLink_cloud" className="c-tab__content">
        <CloudSection
          cloud={cloud}
          onClickedContact={() => onClickedContact('cloud')}
          isProcessing={isProcessing}
        />
      </div>
      <div id="anchorLink_ssl" className="c-tab__content">
        <SslSection
          ssl={ssl}
          onClickedContact={() => onClickedContact('ssl')}
        />
      </div>
      <div id="anchorLink_impersonation" className="c-tab__content">
        <ImpersonationSection
          impersonation={impersonation}
          onClickedContact={() => onClickedContact('impersonation')}
          isProcessing={isProcessing}
        />
      </div>
    </>
  );
}

Result.propTypes = {
  nds: PropTypes.object,
  cloud: PropTypes.object,
  ssl: PropTypes.object,
  impersonation: PropTypes.object,
  onClickedContact: PropTypes.func.isRequired,
  isProcessing: PropTypes.bool,
};

export default Result;
