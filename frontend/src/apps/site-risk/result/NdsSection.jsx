import PropTypes from 'prop-types';
import { useState } from 'react';
import Button from '../../../common/components/Button';
import RankImage from '../../../common/components/RankImage';
import { formatDatetime } from '../../../common/utils';
import Badge from './Badge';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const LEVELS = {
  critical: { message: '緊急', status: 'alert' },
  high: { message: '高リスク', status: 'high' },
  medium: { message: '中リスク', status: 'warning' },
  low: { message: '低リスク', status: 'low' },
  info: { message: '情報', status: 'info' },
};

const RANKS = {
  A: '問題となる異常は見つかりませんでした。定期的な診断を継続して、セキュリティの健康状態を把握しましょう。',
  B: '問題となる異常は見つかりませんでした。定期的な診断を継続して、セキュリティの健康状態を把握しましょう。',
  C: 'セキュリティに欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。',
  D: 'セキュリティに欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。',
  E: '重大かつ緊急性の高いセキュリティの欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。',
};

const RANKSNOTE = {
  A: '安全です',
  B: '安全です',
  C: '要対策',
  D: '要対策',
  E: '要緊急対応',
};

function NdsSection({ nds, onClickedContact, isProcessing = false }) {
  const [isAllShown, setIsAllShown] = useState(false);

  if (!nds) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
            Webサイト脆弱性診断
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (nds.status && nds.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
            Webサイト脆弱性診断
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  const defaultVisibleCount = 4;

  const visibleDetails = isAllShown
    ? nds.details
    : nds.details.slice(0, defaultVisibleCount);

  return (
    <section>
      <div id="section_nds" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
          Webサイト脆弱性診断
        </h2>
        <div className="c-panel__content">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              <RankImage rank={nds.summary.rank} type="nds" />
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              <Button
                id="contact_nds"
                onClick={onClickedContact}
                variant="accent"
                widthSize="full"
                disabled={isProcessing}
              >
                対策する(無料見積)
              </Button>
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            <p className={`c-panel__note c-panel__note--${nds.summary.rank}`}>
              {RANKSNOTE[nds.summary.rank]}
            </p>
            <p className="c-panel__result">{RANKS[nds.summary.rank]}</p>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--pcNone">
              <Button
                id="contact_nds_sp_upper"
                onClick={onClickedContact}
                variant="accent"
                widthSize="full"
                disabled={isProcessing}
              >
                対策する(無料見積)
              </Button>
            </div>
            {/* spのみ表示 end */}
            <ul className="p-siteRiskSummary">
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.critical > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.critical}
                </span>
                <p className="p-siteRiskSummary__text">緊急</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.high > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.high}
                </span>
                <p className="p-siteRiskSummary__text">高リスク</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.medium > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.medium}
                </span>
                <p className="p-siteRiskSummary__text">中リスク</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.low > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.low}
                </span>
                <p className="p-siteRiskSummary__text">低リスク</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span className="p-siteRiskSummary__count">
                  {nds.summary.levelCounts.info}
                </span>
                <p className="p-siteRiskSummary__text">情報</p>
              </li>
            </ul>
            <div className="p-siteRiskToggle">
              <ul
                className={
                  isAllShown
                    ? 'p-siteRiskAccordionList p-siteRiskAccordionList--all'
                    : 'p-siteRiskAccordionList p-siteRiskAccordionList--hiddden'
                }
              >
                {visibleDetails.map((detail, index) => (
                  <li key={index}>
                    <details className="p-siteRiskAccordion">
                      <summary
                        id={`accordion_nds_${detail.level}_${index}_summary`}
                        className="p-siteRiskAccordion__head"
                      >
                        <h3 id={`accordion_nds_${detail.level}_${index}_h3`}>
                          {detail.title}
                        </h3>
                        <Badge
                          status={LEVELS[detail.level].status}
                          message={LEVELS[detail.level].message}
                        />
                      </summary>
                      <div className="p-siteRiskAccordion__body">
                        {detail.impact && (
                          <dl className="p-siteRiskDl">
                            <dt>・想定される影響</dt>
                            <dd>{detail.impact}</dd>
                          </dl>
                        )}
                        {detail.measure && (
                          <dl className="p-siteRiskDl">
                            <dt>・対策方法</dt>
                            <dd>{detail.measure}</dd>
                          </dl>
                        )}
                      </div>
                    </details>
                  </li>
                ))}
              </ul>
              {nds.details.length > defaultVisibleCount && (
                <div className="p-siteRiskToggle__button">
                  <Button
                    id={`nds_details_more_${!isAllShown}`}
                    onClick={() => setIsAllShown(!isAllShown)}
                    variant="text"
                    widthSize="full"
                  >
                    {isAllShown
                      ? (
                        <>
                          閉じる
                          <span className="p-siteRiskToggle__arrowTop" />
                        </>
                      )
                      : (
                        <>
                          もっと見る
                          <span className="p-siteRiskToggle__arrowBottom" />
                        </>
                      )}
                  </Button>
                </div>
              )}
            </div>
            <div className="p-siteRiskCardGray">
              <p className="p-siteRiskCardGray__text">
                本脆弱性診断では、診断対象サイトに影響を与えない範囲で
                <Button
                  id="nds_link"
                  as="a"
                  href="https://gmo-cybersecurity.com/column/assessment/about/"
                  target="_blank"
                  rel="noopener"
                  variant="textInline"
                  referrerPolicy="strict-origin-when-cross-origin"
                >
                  「ネットワーク診断・Webアプリケーション診断・CMS診断」
                </Button>
                を実施しています。診断結果が「安全です」の場合、「一定の不正アクセス対策」が講じられ、一定の安全性が確保されていることを示します。「より正確な結果」「検知項目に対する対策」をお求めの場合は、以下「対策する(無料見積)」より
                <Button
                  id="contact_nds_text"
                  onClick={onClickedContact}
                  variant="textInline"
                  disabled={isProcessing}
                >
                  お問い合わせください。
                </Button>
              </p>
            </div>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
              <Button
                id="contact_nds_sp"
                onClick={onClickedContact}
                variant="accent"
                widthSize="full"
                disabled={isProcessing}
              >
                対策する(無料見積)
              </Button>
            </div>
            {/* spのみ表示 end */}
          </div>
        </div>
        {nds.summary.assessmentTime && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            本診断結果は、
            {' '}
            {formatDatetime(nds.summary.assessmentTime)}
            時点のものです。診断結果は、
            <Button
              as="a"
              href="https://www.gmo.jp/security/check/agreement/"
              target="_blank"
              rel="noopener"
              variant="textXs"
              referrerPolicy="strict-origin-when-cross-origin"
            >
              利用規約
            </Button>
            をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。
          </p>
        )}
      </div>
    </section>
  );
}

NdsSection.propTypes = {
  nds: PropTypes.shape({
    status: PropTypes.string,
    details: PropTypes.array,
    summary: PropTypes.shape({
      rank: PropTypes.string,
      levelCounts: PropTypes.shape({
        critical: PropTypes.number,
        high: PropTypes.number,
        medium: PropTypes.number,
        low: PropTypes.number,
        info: PropTypes.number,
      }),
      assessmentTime: PropTypes.string,
    }),
  }),
  onClickedContact: PropTypes.func.isRequired,
  isProcessing: PropTypes.bool,
};

export default NdsSection;
