import PropTypes from 'prop-types';
import cloudImageCloud from '../../../assets/img/rank_cloud.png';
import cloudImageWarning from '../../../assets/img/rank_warning.png';
import Button from '../../../common/components/Button';
import { formatDatetime } from '../../../common/utils';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const CLOUD_IMAGE_CLOUD = cloudImageCloud;
const CLOUD_IMAGE_WARNING = cloudImageWarning;

function CloudSection({ cloud, onClickedContact, isProcessing = false }) {
  if (!cloud) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
            クラウド利用・リスク診断
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (cloud && cloud.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
            クラウド利用・リスク診断
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  return (
    <section>
      <div id="section_cloud" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
          クラウド利用・リスク診断
        </h2>
        <div className="c-panel__content">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              {cloud.summary.isUsed
                ? (
                  <img src={CLOUD_IMAGE_WARNING} alt="warning" />
                )
                : (
                  <img src={CLOUD_IMAGE_CLOUD} alt="cloud" />
                )}
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              {cloud.summary.isUsed && (
                <Button
                  id="contact_cloud"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                  disabled={isProcessing}
                >
                  クラウドの中身も診断
                  <br />
                  (無料お試し)
                </Button>
              )}
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            {cloud.summary.isUsed && (
              <p className="c-panel__note c-panel__note--warning">要確認</p>
            )}
            {cloud.details && cloud.details.length > 0 && (
              <div className="c-panel__result">
                {cloud.details.map((c, idx) => (
                  <p key={idx}>
                    {c.cloud}
                    のクラウドをご利用中です。クラウド環境のセキュリティ障害の99%
                    <span className="c-panel__result c-panel__result--note">
                      ※
                    </span>
                    が、お客様の設定ミスなどに起因するようになるといわれています。設定内容をご確認ください。
                    <br />
                    <span className="c-panel__result c-panel__result--note">
                      ※ Gartner社レポート「Is the Cloud Secure?」より
                    </span>
                  </p>
                ))}
              </div>
            )}
            {/* cloud利用なし */}
            {!cloud.summary.isUsed && (
              <div className="c-panelList__noteWrap c-panelList__noteWrap--center">
                <p className="c-panelList__note">
                  <span className="icon-base icon-sec-attention icon-size16 icon-color-</div>darkGreen" />
                  AWS・Google Cloud・Microsoft Azureは使用していません
                </p>
              </div>
            )}
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--pcNone">
              {cloud.summary.isUsed && (
                <Button
                  id="contact_cloud_sp"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                  disabled={isProcessing}
                >
                  クラウドの中身も診断
                  <br />
                  (無料お試し)
                </Button>
              )}
            </div>
            {/* spのみ表示 end */}
            {/* cloud利用あり */}
            {cloud.summary.isUsed && (
              <div className="p-siteRiskToggle">
                <div className="p-siteRiskAccordionList">
                  <details className="p-siteRiskAccordion">
                    <summary
                      id="accordion_cloud"
                      className="p-siteRiskAccordion__head"
                    >
                      <h3 id="accordion_cloud_h3">
                        <span className="icon-base icon-sec-attention icon-size20 icon-color-orange" />
                        あなたのクラウド、本当に安全ですか？
                      </h3>
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      <dl className="p-siteRiskDl">
                        <dt>
                          ・想定される影響
                          <span className="p-siteRiskDl__text--em">
                            ― 実は大事なクラウドリスク
                          </span>
                        </dt>
                        <dd>
                          クラウドを利用していると、サービスの設定の間違いによって、本来は外部に公開するはずのないデータが見えてしまうことがあります。たとえば、「社内だけで使っているはずのデータが、インターネット上に公開されていた」などが典型的なケースです。クラウドだから安全、というわけではありません。
                        </dd>
                        <dd>
                          また、
                          <Button
                            id="cloud_google_report_link"
                            as="a"
                            href="https://services.google.com/fh/files/misc/threat_horizons_report_h1_2025.pdf"
                            target="_blank"
                            rel="noopener noreferrer"
                            variant="text"
                          >
                            Googleの調査「H1 2025 Threat Horizons Report」
                          </Button>
                          でも、こうした設定ミスを狙う攻撃が、2024年末にかけて増えていく見込みであると報告されています。今後ますます注意が必要です。
                        </dd>
                      </dl>
                      <dl className="p-siteRiskDl">
                        <dt>・対策方法</dt>
                        <dd>
                          どんなデータやサーバーがクラウド上で動いているかを常に把握すること、およびクラウドのセキュリティベストプラクティスを学び、「自分たちのシステムは本当に大丈夫か」を定期的に確認することをおすすめいたします。
                          <br />
                          「まずは、何がどこで動いているのか」を整理し、誤って公開されているものがないかチェックしましょう。万が一の設定ミスが大きなトラブルにつながるのを防ぐためにも、定期的な見直しがとても大切です。セルフチェックのために、自社システムを点検する際に役立つ資料はこちらです。
                        </dd>
                        {cloud.details[0].cloud === 'AWS' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_aws_checklist"
                                  as="a"
                                  href="https://docs.aws.amazon.com/ja_jp/securityhub/latest/userguide/cis-aws-foundations-benchmark.html"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  - AWS セキュリティのチェックリスト（CIS AWS
                                  Foundations Benchmark）
                                </Button>
                              </li>
                              <li>
                                <Button
                                  id="cloud_aws_column"
                                  as="a"
                                  href="https://blog.flatt.tech/archive/category/AWS"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  - AWS クラウドセキュリティに関するコラム
                                </Button>
                              </li>
                              <li>
                                <Button
                                  id="cloud_aws_best_practice"
                                  as="a"
                                  href="https://shisho.dev/ja/methods/aws"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  - AWS クラウドベストプラクティスの解説
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        {cloud.details[0].cloud === 'GoogleCloud' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_google_checklist"
                                  as="a"
                                  href="https://cloud.google.com/security-command-center/docs/security-posture-overview?hl=ja"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  - Google Cloud セキュリティのチェックリスト
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        {cloud.details[0].cloud === 'Azure' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_azure_checklist"
                                  as="a"
                                  href="https://learn.microsoft.com/ja-jp/security/benchmark/azure/overview"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  - Azure セキュリティのチェックリスト
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        <dd>
                          もし専門家に相談したい場合や、設定ミスを総合的にチェックしたい場合はお気軽に「クラウドの中身も診断」より無料でお試しください。
                        </dd>
                      </dl>
                    </div>
                  </details>
                </div>
              </div>
            )}
          </div>
        </div>
        {cloud.summary.createdAt && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            本診断結果は、
            {' '}
            {formatDatetime(cloud.summary.createdAt)}
            時点のものです。診断結果は、
            <Button
              as="a"
              href="https://www.gmo.jp/security/check/agreement/"
              target="_blank"
              rel="noopener"
              variant="textXs"
              referrerPolicy="strict-origin-when-cross-origin"
            >
              利用規約
            </Button>
            をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。
          </p>
        )}
      </div>
    </section>
  );
}

CloudSection.propTypes = {
  cloud: PropTypes.shape({
    status: PropTypes.string,
    summary: PropTypes.shape({
      isUsed: PropTypes.bool,
      createdAt: PropTypes.string,
    }),
    details: PropTypes.arrayOf(PropTypes.shape({ cloud: PropTypes.string })),
  }),
  onClickedContact: PropTypes.func.isRequired,
  isProcessing: PropTypes.bool,
};

export default CloudSection;
