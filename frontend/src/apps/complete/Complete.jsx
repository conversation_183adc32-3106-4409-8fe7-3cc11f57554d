import { useEffect, useState } from 'react';
import receivedImage from '../../assets/img/illust_received.png';
import EmailSection from '../../common/components/EmailSection';
import FqdnSection from '../../common/components/FqdnSection';
import {
  isValidEmail,
  isValidFqdn,
  isValidUrl,
  getFqdn,
} from '../../common/utils';

const RECEIVED_IMAGE = receivedImage;

function Complete() {
  const [storage, setStorage] = useState({
    email: null,
    fqdn: null,
  });
  const [grecaptcha, setGrecaptcha] = useState(null);

  const { email, fqdn } = storage;

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!email) {
      try {
        const state = localStorage.getItem('state');
        if (!state) {
          throw new Error('State not found');
        }
        const parsedState = JSON.parse(state);
        const { email: emailFromStorage, fqdn: fqdnFromStorage } = parsedState;
        if (!emailFromStorage) {
          throw new Error('Email not found');
        }
        setStorage({ email: emailFromStorage, fqdn: fqdnFromStorage });
        localStorage.removeItem('state');
      } catch (err) {
        console.error(err);
        window.history.back();
      }
    }
  }, [email]);

  if (!email) {
    return <></>;
  }

  const handleFqdnSubmit = (value) => {
    if (isValidFqdn(value) || isValidUrl(value)) {
      localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
      window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/email/`;
    } else {
      // TODO: validation error
    }
  };

  const handleEmailSubmit = async (value) => {
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      if (isValidEmail(value)) {
        const response = await fetch(
          `${import.meta.env.VITE_API_HOST}/api/email`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: value, recaptchaToken }),
          },
        );

        if (response.ok) {
          localStorage.setItem('state', JSON.stringify({ email: value }));
          window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/complete/`;
        } else {
          const { status, message } = await response.json();
          if (status === 'error') {
            throw new Error(`Email api error message: ${message}`);
          }
        }
      } else if (isValidFqdn(value) || isValidUrl(value)) {
        localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
        window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/email/`;
      } else {
        // TODO: validation error 表示
      }
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <h1 className="c-title c-title--pdb20">
            {fqdn
              ? 'サイトリスク診断の受付完了'
              : 'パスワード漏洩診断の受付完了'}
          </h1>
          <p className="p-base__text">
            {fqdn
              ? '診断結果のお届けには約30分ほどお時間をいただきます。'
              : 'メール内のリンクから診断結果をご確認いただけます。'}
          </p>
          <div className="c-panel c-panel--s">
            <div className="c-panel__img">
              <img src={RECEIVED_IMAGE} alt="" />
            </div>
            <p className="c-panel__text">
              診断が完了しましたら、ご入力いただいたメールアドレス
            </p>
            <p className="c-panel__text">
              {email && <span className="c-panel__emailAddress">{email}</span>}
              に診断結果報告メールを送信します。
            </p>

            <div className="c-panel__annotation c-panel__annotation--center">
              ※メールが届かない場合は、迷惑メールフォルダをご確認いただくか、もう一度入力してください。
            </div>
          </div>
        </div>
        <div className="p-base__sub">
          {fqdn
            ? (
              <EmailSection onSubmit={handleEmailSubmit} />
            )
            : (
              <FqdnSection onSubmit={handleFqdnSubmit} />
            )}
        </div>
      </div>
    </section>
  );
}

export default Complete;
