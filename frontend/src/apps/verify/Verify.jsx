import { useEffect, useState } from 'react';
import Button from '../../common/components/Button';

function Verify() {
  const [state, setState] = useState(null);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isNotConfirm, setIsNotConfirm] = useState(false);

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!state) {
      try {
        const s = localStorage.getItem('state');
        if (!s) {
          throw new Error('State not found');
        }
        const sJson = JSON.parse(s);
        if (!sJson.email || !sJson.fqdn || !sJson.authTxt) {
          throw new Error('email, fqdn or authTxt not found');
        }
        setState(sJson);
        localStorage.removeItem('state');
      } catch (err) {
        console.error(err);
        window.history.back();
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCopyClick = (text) => {
    navigator.clipboard.writeText(text);
  };

  const handleProceed = async () => {
    setIsProcessing(true);
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/fqdn`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: state.email,
            fqdn: state.fqdn,
            recaptchaToken,
          }),
        },
      );

      if (response.ok) {
        const { status } = await response.json();
        localStorage.setItem(
          'state',
          JSON.stringify({ email: state.email, fqdn: state.fqdn }),
        );
        if (status === 'success') {
          window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/complete/`;
        } else if (status === 'request') {
          setIsNotConfirm(true);
        }
      } else {
        const { status, message } = await response.json();
        if (status === 'error') {
          throw new Error(`Fqdn api error message: ${message}`);
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!state || !state.email || !state.fqdn || !state.authTxt) {
    return <></>;
  }

  return (
    <section className="p-base">
      <div className="p-base__inner">
        <h1 className="c-title c-title--pdb20">ドメインの所有確認</h1>
        <p className="p-base__text">
          セキュリティ診断を安全に実施するため、
          <br />
          ドメインの所有者様であることを確認させていただきます。
        </p>
        <div className="c-panel">
          <div className="c-panel__info">
            診断対象のドメイン（例：~.com / ~.jp /
            ~.co.jp）とメールアドレスが一致しませんでした。
            <br />
            以下のドメイン所有者確認を行ってください。
          </div>
          <h2 className="c-panel__subTitle">
            以下の手順に従ってDNS設定を行ってください
          </h2>
          <div className="c-panel__main">
            <div className="p-verify">
              <ol className="p-verifyStep">
                <li>
                  <h3 className="p-verifyStep__title p-verifyStep__title--step01">
                    DNS管理画面にログイン
                  </h3>
                  <p>
                    お使いのドメインレジストラまたはDNSサービスの管理画面にアクセスしてください。
                  </p>
                </li>
                <li>
                  <h3 className="p-verifyStep__title p-verifyStep__title--step02">
                    以下のTXTレコードを追加してください
                  </h3>
                  <dl className="p-verifyStepContent">
                    <dt>ホスト名</dt>
                    <dd>
                      <code>{state.fqdn}</code>
                      <div className="c-button-copy">
                        <div className="c-button-copy__message">
                          コピーしました
                        </div>
                        <Button
                          onClick={() => handleCopyClick(state.fqdn)}
                          variant="text"
                          id="js-copyButton"
                        >
                          <span className="icon-base icon-sec-copy icon-size20 icon-color-darkGreen" />
                        </Button>
                      </div>
                    </dd>
                  </dl>
                  <dl className="p-verifyStepContent">
                    <dt>値</dt>
                    <dd>
                      <code>{`gmo-security=${state.authTxt}`}</code>
                      <div className="c-button-copy">
                        <div className="c-button-copy__message">
                          コピーしました
                        </div>
                        <Button
                          onClick={() =>
                            handleCopyClick(`gmo-security=${state.authTxt}`)}
                          variant="text"
                          id="js-copyButton"
                        >
                          <span className="icon-base icon-sec-copy icon-size20 icon-color-darkGreen" />
                        </Button>
                      </div>
                    </dd>
                  </dl>
                </li>
                <li>
                  <h3 className="p-verifyStep__title p-verifyStep__title--step03">
                    設定内容を保存
                  </h3>
                  <p>
                    変更内容を保存し、DNS設定を更新してください。詳細はご利用中のサービスへお問い合わせください。
                  </p>
                </li>
              </ol>
              <p className="p-verify__attention">
                <span className="icon-base icon-sec-attention icon-size16 icon-color-darkGreen" />
                DNS設定が反映されるまで最大24時間かかる場合があります。設定が完了すると自動的に診断が開始され、結果をメールでお知らせします。
              </p>
              <p className="p-verify__note">
                ※DNS設定が難しい場合は、サポートページをご確認いただくか、システム管理者にお問い合わせください。
              </p>
              <div className="p-verify__agreement">
                <label className="p-verify__label">
                  <input
                    type="checkbox"
                    checked={isChecked}
                    onChange={e => setIsChecked(e.target.checked)}
                    className="c-inputCheckBox"
                  />
                  <span>TXTレコードを追加しました</span>
                </label>
              </div>
              {isNotConfirm && (
                <div className="c-panel__error">
                  <span className="icon-base icon-sec-caution" />
                  設定が確認できません。再度DNS設定を確認してください。
                </div>
              )}
              <div className="p-verify__button">
                <Button
                  onClick={handleProceed}
                  disabled={!isChecked || isProcessing}
                  variant="primary"
                  widthSize="full"
                  isLoading={isProcessing}
                >
                  診断を開始する
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Verify;
