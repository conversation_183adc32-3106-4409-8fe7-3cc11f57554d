import PropTypes from 'prop-types';
import { useMediaQuery } from 'react-responsive';

function Tabs({ activeTab, onTabChange }) {
  const TABS = [
    { id: 'diagnostic', label: { pc: 'パスワード漏洩・Webサイトリスク診断', sp: '診断' } },
    { id: 'chat', label: { pc: 'その他のセキュリティ相談', sp: '相談' } },
  ];

  const isMobile = useMediaQuery({ query: '(max-width: 600px)' });

  return (
    <div className="p-homeTabs" role="tablist" aria-labelledby="tablist-label">
      {TABS.map((tab) => {
        return (
          <button
            type="button"
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            id={tab.id === 'diagnostic' ? 'tab1' : 'tab2'}
            role="tab"
            aria-controls={
              tab.id === 'diagnostic' ? 'tab-panel1' : 'tab-panel2'
            }
            aria-selected={`${activeTab === tab.id ? 'true' : 'false'}`}
            className={`${activeTab === tab.id ? 'p-homeTabs__list p-homeTabs__list--active' : 'p-homeTabs__list'}
              ${tab.id === 'brandTld' ? 'p-homeTabs__list--arrow' : ''}`}
          >
            <span
              className={`icon-base ${tab.id === 'diagnostic'
                ? 'icon-size20 icon-sec-impersonation'
                : tab.id === 'chat'
                  ? 'icon-size24 icon-sec-other'
                  : ''}
                  ${activeTab === tab.id ? 'icon-color-green' : 'icon-color-gray70'}`}
            />
            {tab.label[isMobile ? 'sp' : 'pc']}
          </button>
        );
      })}
      <a
        id="top_tab_yourbrand_link"
        href="/security/yourbrand/"
        target="_blank"
        rel="noopener"
        className="p-homeTabs__list p-homeTabs__list--arrow"
      >
        <span
          className="icon-base
        icon-size20 icon-sec-www"
        />
        {isMobile ? '「.貴社名」' : '「.貴社名」取得はこちら'}
      </a>
    </div>
  );
}

Tabs.propTypes = {
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired,
};

export default Tabs;
