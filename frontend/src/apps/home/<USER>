import PropTypes from 'prop-types';
import logoImage from '../../assets/img/logo-v2.svg';

const LOGO_IMAGE = logoImage;

function Header({ title, description }) {
  return (
    <div className="p-homeHeader">
      <h1>
        <img src={LOGO_IMAGE} alt={title} />
      </h1>
      {description && <p className="text-gray-600">{description}</p>}
    </div>
  );
}

Header.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
};

export default Header;
