import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import Button from '../../common/components/Button';

function SearchInput({
  onSubmit,
  isLoading = false,
  placeholder = 'メールアドレス or URLを入力',
  buttonText = '無料診断',
  initValue = '',
  onChange = () => { },
}) {
  const [value, setValue] = useState(initValue);

  useEffect(() => {
    setValue(initValue);
  }, [initValue]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (value.trim()) {
      onSubmit(value.trim());
    }
  };

  const handleChangeValue = (value) => {
    setValue(value);
    onChange && onChange(value);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="c-inputSearch">
        <input
          type="text"
          value={value}
          onChange={e => handleChangeValue(e.target.value)}
          placeholder={placeholder}
          className="c-inputSearch__input"
        />
        <div className="c-inputSearch__button">
          <Button type="submit" disabled={isLoading} variant="search">
            {isLoading
              ? (
                <div className="c-loader c-loader--button" />
              )
              : buttonText === '相談'
                ? (
                  <span className="icon-base icon-sec-chatbubble-bold icon-size16 icon-color-black" />
                )
                : (
                  <span className="icon-base icon-sec-search-bold icon-size16 icon-color-black" />
                )}
            <span className="c-inputSearch__buttonText">{buttonText}</span>
          </Button>
        </div>
      </div>
    </form>
  );
}

SearchInput.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  placeholder: PropTypes.string,
  buttonText: PropTypes.string,
  initValue: PropTypes.string,
  onChange: PropTypes.func,
};

export default SearchInput;
