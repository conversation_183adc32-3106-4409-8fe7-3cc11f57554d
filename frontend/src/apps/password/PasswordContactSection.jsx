import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import Button from '../../common/components/Button';
import ContactComplete from '../../common/components/ContactComplete';
import { isValidEmail } from '../../common/utils';

export const PasswordContactSection = ({
  code,
  email,
  isCompleted,
  setIsCompleted,
}) => {
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastName, setLastName] = useState('');
  const [firstName, setFirstName] = useState('');
  const [consultation, setConsultation] = useState('');
  const [showEmailField, setShowEmailField] = useState(false);
  const [inputEmail, setInputEmail] = useState('');

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  const handleSubmit = async () => {
    setIsProcessing(true);

    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/contact`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            code,
            targets: ['password'],
            email: inputEmail || email,
            fullname: `${lastName} ${firstName}`,
            consultation,
            recaptchaToken,
          }),
        },
      );

      if (response.ok) {
        setIsCompleted(true);
      } else {
        const { status, message } = await response.json();
        if (status === 'error') {
          throw new Error(`Contact api error message: ${message}`);
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsProcessing(false);
    }
  };

  const validate = () => {
    return (
      lastName.length > 0
      && firstName.length > 0
      && (!showEmailField || (showEmailField && isValidEmail(inputEmail)))
    );
  };

  return (
    <section id="anchorLink_contact">
      <div id="section_password_contact" className="c-contact gtm-view">
        <h2 className="c-title c-title--pdb20">
          <span className="c-title__subhead">法人様向け</span>
          専門家へ相談する
        </h2>
        <p className="c-contact__text">
          お困りごとがございましたら、以下のフォームのご入力のうえ「相談する」ボタンをクリックしてください。
          <br />
          セキュリティの専門家からメールでご連絡いたします。
        </p>
        <div className="c-contact__form">
          {isCompleted
            ? (
              <ContactComplete
                email={inputEmail || email}
                setIsCompleted={setIsCompleted}
              />
            )
            : (
              <div className="c-panel">
                <div className="c-inputForm">
                  <div className="c-inputForm__column">
                    <div className="c-inputForm__columnBox">
                      <label htmlFor="lastName" className="c-input__label">
                        姓
                        <span className="c-input__required">※</span>
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        className="c-input__input"
                        value={lastName}
                        onChange={e => setLastName(e.target.value)}
                      />
                    </div>
                    <div className="c-inputForm__columnBox">
                      <label htmlFor="firstName" className="c-input__label">
                        名
                        <span className="c-input__required">※</span>
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        className="c-input__input"
                        value={firstName}
                        onChange={e => setFirstName(e.target.value)}
                      />
                    </div>
                  </div>
                  {showEmailField && (
                    <div className="c-inputForm__box">
                      <label htmlFor="email" className="c-input__label">
                        メールアドレス
                        <span className="c-input__required">※</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        placeholder="メールアドレスを入力してください"
                        className="c-input__input"
                        value={inputEmail}
                        required
                        onChange={e => setInputEmail(e.target.value)}
                      />
                    </div>
                  )}
                  <div className="c-inputForm__box">
                    <label htmlFor="consultation" className="c-input__label">
                      ご相談内容（任意記入欄）
                    </label>
                    <input
                      type="text"
                      id="consultation"
                      placeholder="例）セキュリティ対策を強化したい"
                      className="c-input__input"
                      value={consultation}
                      onChange={e => setConsultation(e.target.value)}
                    />
                  </div>
                  <div className="c-inputForm__button">
                    <Button
                      variant="primary"
                      widthSize="full"
                      disabled={!validate() || isProcessing}
                      isLoading={isProcessing}
                      onClick={handleSubmit}
                    >
                      相談する
                    </Button>
                  </div>
                  {!showEmailField && (
                    <div className="c-inputForm__note">
                      <Button
                        id="input_another_email"
                        variant="text"
                        onClick={() => setShowEmailField(true)}
                      >
                        診断に利用したメールアドレス以外を使用する
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
        </div>
      </div>
    </section>
  );
};

PasswordContactSection.propTypes = {
  code: PropTypes.string.isRequired,
  email: PropTypes.string.isRequired,
  isCompleted: PropTypes.bool.isRequired,
  setIsCompleted: PropTypes.func.isRequired,
};

export default PasswordContactSection;
