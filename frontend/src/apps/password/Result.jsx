import PropTypes from 'prop-types';
import resultImageAlert from '../../assets/img/rank_alert.png';
import resultImageSafe from '../../assets/img/rank_safe.png';
import Button from '../../common/components/Button';
import { formatDatetime } from '../../common/utils';
import Detail from './Detail';
import PasswordPeriodicCheckup from './PasswordPeriodicCheckup';

const RESULT_IMAGE_ALERT = resultImageAlert;
const RESULT_IMAGE_SAFE = resultImageSafe;

function Result({ code, summary, details, configuration, onClickedContact }) {
  const handleNewDiagnostic = () => {
    window.open(`${import.meta.env.VITE_PATH_PREFIX}/`, '_blank');
  };

  return (
    <div id="section_password" className="c-panel gtm-view">
      <div className="c-panel__content c-panel__content--gap20">
        <div className="c-panel__side">
          {details.length > 0
            ? (
              <div className="c-panel__rankImg">
                <img src={RESULT_IMAGE_ALERT} alt="alert" />
              </div>
            )
            : (
              <div className="c-panel__rankImg">
                <img src={RESULT_IMAGE_SAFE} alt="safe" />
              </div>
            )}
          {/* pcのみ表示 */}
          <div className="c-panel__sideButton c-panel__sideButton--spNone">
            <p className="c-panel__sideButtonText">法人様向け</p>
            <Button
              id="contact_password"
              onClick={() => {
                onClickedContact();
                const element = document.getElementById('anchorLink_contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              variant="accent"
              widthSize="full"
            >
              専門家へ相談する
            </Button>
          </div>
          {/* pcのみ表示 end */}
        </div>
        <div className="c-panel__main">
          {details.length > 0
            ? (
              <>
                <p className="c-panel__note c-panel__note--alert">要注意</p>
                <p className="c-panel__result">
                  診断したメールアドレスで利用中のサービスにおいてパスワードが漏洩している可能性があります。
                </p>
              </>
            )
            : (
              <>
                <p className="c-panel__note">安全です</p>
                <p className="c-panel__result">
                  診断したメールアドレスで利用中のサービスにおいてパスワード漏洩はありません。
                </p>
              </>
            )}
          {/* spのみ表示 */}
          <div className="c-panel__sideButton c-panel__sideButton--pcNone">
            <p className="c-panel__sideButtonText">法人様向け</p>
            <Button
              id="contact_password_sp_upper"
              onClick={() => {
                onClickedContact();
                const element = document.getElementById('anchorLink_contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              variant="accent"
              widthSize="full"
            >
              専門家へ相談する
            </Button>
          </div>
          {/* spのみ表示 end */}
          <div className="p-passwordResult">
            {/* spのみ表示 */}
            {details.length > 0 && (
              <div className="p-passwordResult__note p-passwordResult__note--pcNone">
                <dl className="p-passwordResultNote">
                  <dt>対応方法</dt>
                  <dd>
                    <ul>
                      <li>
                        ・該当サービスのパスワードを変更し、他サービスでも同じパスワードをご利用の場合はすべて変更してください
                      </li>
                      <li>・パスワード設定は最大文字数を推奨します</li>
                      <li>
                        ・二要素認証、二段階認証可能なサービスは設定を推奨します
                      </li>
                      <li>
                        ・ワンタイムパスワード、IP制限が可能なサービスは設定を推奨します
                      </li>
                    </ul>
                  </dd>
                </dl>
                <dl className="p-passwordResultNote p-passwordResultNote--normal">
                  <dt>企業でご利用のメールアドレス</dt>
                  <dd>
                    社員のパスワード漏洩がご心配な情報システム担当者様や経営者の皆様は、「専門家へ相談する」のボタンよりお気軽にご相談ください。
                  </dd>
                </dl>
              </div>
            )}
            {/* spのみ表示 end */}
            <div className="p-passwordResult__email">
              <div className="c-panelBox">
                {summary.email && (
                  <dl className="c-panelBox__item">
                    <dt>診断したメールアドレス</dt>
                    <dd>{summary.email}</dd>
                  </dl>
                )}
                <div className="c-panelBox__button">
                  <Button
                    id="another_email"
                    onClick={handleNewDiagnostic}
                    external
                    exIcon="small"
                    variant="secondary"
                  >
                    他のメールアドレスを診断
                  </Button>
                </div>
              </div>
            </div>
            <div className="p-passwordResult__summary">
              <div
                className={
                  details.length > 0
                    ? 'p-passwordResult__count p-passwordResult__count--error'
                    : 'p-passwordResult__count p-passwordResult__count--safe'
                }
              >
                {details.length}
              </div>
              {details.length > 0
                ? (
                  <p>パスワードが漏洩した可能性のあるサイト</p>
                )
                : (
                  <p>情報漏洩は検出されませんでした</p>
                )}
            </div>
            <div className="p-passwordResult__detail">
              {/* pcのみ表示 */}
              {details.length > 0 && (
                <div className="p-passwordResult__note p-passwordResult__note--spNone">
                  <dl className="p-passwordResultNote">
                    <dt>対応方法</dt>
                    <dd>
                      <ul>
                        <li>
                          ・該当サービスのパスワードを変更し、他サービスでも同じパスワードをご利用の場合はすべて変更してください
                        </li>
                        <li>・パスワード設定は最大文字数を推奨します</li>
                        <li>
                          ・二要素認証、二段階認証可能なサービスは設定を推奨します
                        </li>
                        <li>
                          ・ワンタイムパスワード、IP制限が可能なサービスは設定を推奨します
                        </li>
                      </ul>
                    </dd>
                  </dl>
                  <dl className="p-passwordResultNote p-passwordResultNote--normal">
                    <dt>企業でご利用のメールアドレス</dt>
                    <dd>
                      社員のパスワード漏洩がご心配な情報システム担当者様や経営者の皆様は、「専門家へ相談する」のボタンよりお気軽にご相談ください。
                    </dd>
                  </dl>
                </div>
              )}
              {/* pcのみ表示 end */}
              <PasswordPeriodicCheckup
                code={code}
                nextCheckedAt={configuration?.nextCheckedAt}
                isRegularly={configuration?.isRegularly}
                interval={configuration?.interval}
                isNotification={configuration?.isNotification}
                createdAt={summary.createdAt}
              />
              {details.length > 0 && (
                <div className="p-passwordDetailWrap">
                  <ul className="c-panelList">
                    {details.map((detail, index) => (
                      <li key={index}>
                        <Detail {...detail} />
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
          {/* spのみ表示 */}
          <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
            <p className="c-panel__sideButtonText">法人様向け</p>
            <Button
              id="contact_password_sp"
              onClick={() => {
                onClickedContact();
                const element = document.getElementById('anchorLink_contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              variant="accent"
              widthSize="full"
            >
              専門家へ相談する
            </Button>
          </div>
          {/* spのみ表示 end */}
        </div>
      </div>
      {summary.createdAt && (
        <p className="c-panel__annotation c-panel__annotation--xs">
          本診断結果は、
          {formatDatetime(summary.createdAt)}
          時点のものです。診断結果は、
          <Button
            as="a"
            href="https://www.gmo.jp/security/check/agreement/"
            target="_blank"
            rel="noopener"
            variant="textXs"
            referrerPolicy="strict-origin-when-cross-origin"
          >
            利用規約
          </Button>
          をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。
        </p>
      )}
    </div>
  );
}

Result.propTypes = {
  code: PropTypes.string.isRequired,
  summary: PropTypes.shape({
    email: PropTypes.string,
    createdAt: PropTypes.string,
  }).isRequired,
  details: PropTypes.arrayOf(PropTypes.object).isRequired,
  configuration: PropTypes.shape({
    nextCheckedAt: PropTypes.string,
    isRegularly: PropTypes.bool,
    interval: PropTypes.number,
    isNotification: PropTypes.bool,
  }),
  onClickedContact: PropTypes.func.isRequired,
};

export default Result;
