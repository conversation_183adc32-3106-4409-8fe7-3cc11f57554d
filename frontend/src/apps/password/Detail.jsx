import PropTypes from 'prop-types';

function Detail({ title, addedDate }) {
  return (
    <div className="p-passwordDetail">
      <div className="p-passwordDetail__title">{title}</div>
      {/* TODO: 確認 漏洩した日 */}
      {addedDate && (
        <div className="p-passwordDetail__date">
          漏洩した日
          {' '}
          {addedDate.split('T')[0].replaceAll('-', '/')}
        </div>
      )}
    </div>
  );
}

Detail.propTypes = {
  title: PropTypes.string.isRequired,
  addedDate: PropTypes.string,
};

export default Detail;
