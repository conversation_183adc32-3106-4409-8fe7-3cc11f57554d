import { useState, useCallback } from 'react';
import { useMediaQuery } from 'react-responsive';

const useModal = () => {
  const [isVisible, setIsVisible] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [isClosable, setIsClosable] = useState(true);

  const [el] = document.getElementsByTagName('body');
  const isMobile = useMediaQuery({ query: '(max-width: 600px)' });
  const headerOffset = isMobile ? 0 : 29;

  const show = useCallback(() => {
    if (isVisible) return;
    setIsVisible(true);
    el.style.top = `-${window.scrollY - headerOffset}px`;
    el.style.width = '100%';
    el.style.position = 'fixed';
  }, []);

  const hide = useCallback(() => {
    if (!isClosable) return;
    setIsVisible(false);

    const top = el.style.top;

    el.style.top = '';
    el.style.width = '';
    el.style.position = '';

    const topInt = parseInt(top || '0');
    window.scrollTo(0, topInt * -1 + (topInt !== 0 ? headerOffset : 0));
  }, []);

  // eslint-disable-next-line no-unused-vars
  const setIsModalClosable = useCallback((isC) => {
    // setIsClosable(isC);
  }, []);

  const Modal = ({ children }) => {
    if (!isVisible) return null;

    return (
      <div className="c-modal">
        <div className="c-modal__overlay" onClick={hide} />
        <div className="c-modal__content">
          {isClosable && (
            <button className="c-modal__close" onClick={hide}>
              <span className="icon-base icon-sec-close icon-size20 icon-color-darkGreen" />
            </button>
          )}
          {children}
        </div>
      </div>
    );
  };

  return {
    show,
    Modal,
    setIsModalClosable,
  };
};

export default useModal;
