import { useState } from 'react';
import { ErrorMessages } from '../messages/error';

const LIMITS = {
  MINUTE: 5,
  DAY: 50,
};
const STORAGE_KEY = 'chat_limits';

const getToday = () => new Date().toDateString();

const getLimitsData = () => {
  const data = localStorage.getItem(STORAGE_KEY);
  return data
    ? JSON.parse(data)
    : {
      minuteCount: 0,
      dayCount: 0,
      minuteTimestamp: 0,
      dayTimestamp: getToday(),
    };
};

const updateLimitsData = (data) => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
};

export const useChatLimiter = () => {
  const [error, setError] = useState('');

  const checkChatLimits = () => {
    const limitsData = getLimitsData();
    const now = Date.now();

    if (now - limitsData.minuteTimestamp > 60_000) {
      limitsData.minuteCount = 0;
      limitsData.minuteTimestamp = now;
    }

    if (limitsData.dayTimestamp !== getToday()) {
      limitsData.dayCount = 0;
      limitsData.dayTimestamp = getToday();
    }

    updateLimitsData(limitsData);

    const isMinuteLimitReached = limitsData.minuteCount >= LIMITS.MINUTE;
    const isDayLimitReached = limitsData.dayCount >= LIMITS.DAY;

    if (isMinuteLimitReached) {
      setError(ErrorMessages.CHAT_REACHED_MINUTE_LIMIT);
    } else if (isDayLimitReached) {
      setError(ErrorMessages.CHAT_REACHED_DAY_LIMIT);
    } else {
      setError('');
    }

    return !isMinuteLimitReached && !isDayLimitReached;
  };

  const incrementChatCounter = () => {
    const limitsData = getLimitsData();
    limitsData.minuteCount += 1;
    limitsData.dayCount += 1;
    limitsData.minuteTimestamp = limitsData.minuteTimestamp || Date.now();
    limitsData.dayTimestamp = getToday();
    updateLimitsData(limitsData);
  };

  const clearError = () => {
    setError('');
  };

  return {
    checkChatLimits,
    clearError,
    error,
    incrementChatCounter,
  };
};
