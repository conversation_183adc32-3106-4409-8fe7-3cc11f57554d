import PropTypes from 'prop-types';
import receivedImage from '../../assets/img/illust_received.png';
import Button from '../../common/components/Button';

const RECEIVED_IMAGE = receivedImage;

function ContactComplete({ email, setIsCompleted }) {
  return (
    <div className="c-panel">
      <h3 className="c-panel__title c-panel__title--darkGreen">
        相談受付が完了しました
      </h3>
      <div className="c-panel__img">
        <img src={RECEIVED_IMAGE} alt="" />
      </div>
      <p className="c-panel__text">
        専門家より
        {email && <span className="c-panel__emailAddress">{email}</span>}
        に
        <span className="c-panel__textBreak">ご連絡いたします。</span>
      </p>
      <p className="c-panel__annotation c-panel__annotation--center">
        ※メールが届かない場合は、迷惑メールフォルダをご確認いただくか、
        <Button variant="text" onClick={() => setIsCompleted(false)}>
          もう一度入力
        </Button>
        してください。
      </p>
    </div>
  );
}

ContactComplete.propTypes = {
  email: PropTypes.string,
  setIsCompleted: PropTypes.func.isRequired,
};

export default ContactComplete;
