import PropTypes from 'prop-types';
import { useState } from 'react';
import Button from './Button';

function FqdnInputForm({ onSubmit }) {
  const [fqdn, setFqdn] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    if (fqdn.trim()) {
      onSubmit(fqdn.trim());
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="c-inputSearch">
        <label htmlFor="fqdn" className="sr-only"></label>
        <input
          type="text"
          id="fqdn"
          value={fqdn}
          onChange={e => setFqdn(e.target.value)}
          placeholder="URLを入力"
          className="c-inputSearch__input c-inputSearch__input--border"
        />
        <div className="c-inputSearch__button">
          <Button type="submit" variant="search" disabled={isSubmitting}>
            {isSubmitting
              ? (
                <span className="c-loader c-loader--button" />
              )
              : (
                <span className="icon-base icon-sec-search-bold icon-size16 icon-color-black" />
              )}
            <span className="c-inputSearch__buttonText">無料診断</span>
          </Button>
        </div>
      </div>
    </form>
  );
}

FqdnInputForm.propTypes = { onSubmit: PropTypes.func.isRequired };

export default FqdnInputForm;
