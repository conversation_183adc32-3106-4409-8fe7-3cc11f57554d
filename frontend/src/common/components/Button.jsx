import PropTypes from 'prop-types';

function Button({
  children,
  isLoading,
  as = 'button',
  external = false,
  exIcon = '',
  variant = 'primary',
  widthSize = '',
  className = '',
  ...props
}) {
  const variantStyles = {
    primary: 'c-button-primary',
    secondary: 'c-button-secondary',
    accent: 'c-button-accent',
    search: 'c-button-search',
    chat: 'c-button-chat',
    brandTldPrimary: 'c-button-brandTld-primary',
    brandTldAccent: 'c-button-brandTld-accent',
    image: 'c-button-image',
    text: 'c-button-text',
    textXs: 'c-button-text c-button-text--xs',
    textInline: 'c-button-text c-button-text--inline',
    textUnderLine: 'c-button-text c-button-text--underline',
  };
  const widthStyles = widthSize === 'full' ? 'c-button-size-full' : '';
  const exIconStyles
    = exIcon === 'large'
      ? 'icon-base icon-sec-link icon-size16'
      : exIcon === 'small'
        ? 'icon-base icon-sec-link icon-size12'
        : exIcon === 'xsmall'
          ? 'icon-base icon-sec-link icon-size10'
          : '';

  const disabledStyles = isLoading || props.disabled ? '' : '';

  const Component = as;

  return (
    <Component
      className={`${variantStyles[variant]} ${widthStyles} ${disabledStyles} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && <div className="c-loader c-loader--button" />}
      {children}
      {external && <span className={`${exIconStyles}`} />}
    </Component>
  );
}

Button.propTypes = {
  children: PropTypes.node,
  isLoading: PropTypes.bool,
  as: PropTypes.oneOfType([PropTypes.string, PropTypes.elementType]),
  external: PropTypes.bool,
  exIcon: PropTypes.string,
  variant: PropTypes.string,
  widthSize: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
};

export default Button;
