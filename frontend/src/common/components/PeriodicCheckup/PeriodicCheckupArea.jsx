import PropTypes from 'prop-types';

const PeriodicCheckupArea = ({ children, nextDateStr }) => {
  return (
    <div className="c-periodicCheck">
      <div className="c-periodicCheck__head">
        <div className="c-periodicCheck__textWrap">
          <div className="c-periodicCheck__icon">
            <span className="icon-base icon-sec-cycle icon-size16 icon-color-darkGreen" />
          </div>
          <p className="c-periodicCheck__text">
            定期診断を無償で行い、 メールでお知らせします。
          </p>
        </div>
        <div className="c-periodicCheck__list">{children}</div>
      </div>
      {nextDateStr && (
        <div className="c-periodicCheck__date">
          次回診断日：
          {nextDateStr}
        </div>
      )}
    </div>
  );
};

PeriodicCheckupArea.propTypes = {
  children: PropTypes.node.isRequired,
  nextDateStr: PropTypes.string,
};

export default PeriodicCheckupArea;
