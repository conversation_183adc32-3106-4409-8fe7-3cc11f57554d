import PropTypes from 'prop-types';
import FqdnInputForm from './FqdnInputForm';

function FqdnSection({ onSubmit }) {
  return (
    <section className="c-subSection">
      <div className="c-subSection__inner">
        <h2 className="c-title c-title--color-white">
          Webサイト
          <span>リスク診断も無料です</span>
          <span className="c-title__sub c-title__sub--color-white">
            Website Risk Assessment
          </span>
        </h2>
        <p className="c-subSection__text">
          WEBの侵入リスクなどを無料でお調べいたします
        </p>
        <div className="c-subSection__form">
          <FqdnInputForm onSubmit={onSubmit} />
        </div>
        <p className="c-subSection__text">
          「 脆弱性診断 」
          <span>「 クラウド利用・リスク診断 」</span>
          <span>「実在証明・盗聴防止（SSL）診断」 </span>
          <span>
            <br />
            「なりすまし診断」を同時に実施します。
          </span>
        </p>
        <p className="c-subSection__note">診断結果は順次表示されます。</p>
        {/* <div className="c-subSection__img">
          <img src={FQDN_SECTION_IMAGE} alt="" width="582" height="192" />
        </div> */}
      </div>
    </section>
  );
}

FqdnSection.propTypes = { onSubmit: PropTypes.func.isRequired };

export default FqdnSection;
