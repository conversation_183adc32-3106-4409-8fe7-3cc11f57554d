import PropTypes from 'prop-types';
import EmailInputForm from './EmailInputForm';

function EmailSection({ onSubmit }) {
  return (
    <section className="c-subSection">
      <div className="c-subSection__inner">
        <h2 className="c-title c-title--color-white">
          パスワード漏洩診断も無料です
          <span className="c-title__sub c-title__sub--color-white">
            Password Leak Detection
          </span>
        </h2>
        <p className="c-subSection__text">
          メールアドレスを入力して、パスワード漏洩の有無を無料でお調べいたします。
        </p>
        <div className="c-subSection__form">
          <EmailInputForm onSubmit={onSubmit} />
        </div>
        {/* <div className="c-subSection__img">
          <img src={EMAIL_SECTION_IMAGE} alt="" width="485" height="200" />
        </div> */}
      </div>
    </section>
  );
}

EmailSection.propTypes = { onSubmit: PropTypes.func.isRequired };

export default EmailSection;
