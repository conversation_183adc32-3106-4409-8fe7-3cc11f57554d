import PropTypes from 'prop-types';
import errorImage from '../../assets/img/rank_alert.png';

const ERROR_IMAGE = errorImage;

function Error({ text }) {
  if (!text) return <></>;

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <div className="c-panel c-panel--s">
            <h1 className="c-panel__title c-panel__title--error">{text}</h1>
            <div className="c-panel__main">
              <div className="c-panel__img">
                <img src={ERROR_IMAGE} alt="alert" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

Error.propTypes = { text: PropTypes.string };

export default Error;
