@charset "utf-8";

@use '../global/variables' as *;

/*すべてoverviewのタブデザインにしか適用されないclass*/

.c-tab {
  &__btn {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: -20px;
      left: 0;
      width: 100%;
      height: 20px;
      box-shadow: 0 -16px 16px rgba(0, 0, 0, 0.04);
    }
    &--active {
      box-shadow: 0 -14px 14px rgba(0, 0, 0, 0.02);
      &::before {
        display: none;
      }
    }
  }
  &__content {
    display: none;
    &--active {
      display: block;
    }
  }
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-tab {
    &__btn {
      position: relative;
      border: 1px solid $color-white;
      &::before {
        display: none;
      }
      &--active {
        border: 1px solid $color-darkGreen;
        &::before {
          display: none;
        }
      }
    }
  }
}
