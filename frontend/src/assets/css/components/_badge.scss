@charset "utf-8";

@use '../global/variables' as *;

.c-badge {
  @mixin c-badge($font-color: $color-white, $bg-color: $color-error) {
    font-size: 12px;
    font-weight: 300;
    color: $font-color;
    background-color: $bg-color;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    column-gap: 4px;
    padding: 2px 12px;
    border-radius: $border-radius-round;
  }
  @include c-badge;
  &--hidden {
    display: none;
  }
  &--high {
    @include c-badge($color-orange-20, $color-orange-alpha10);
  }
  &--warning {
    @include c-badge($color-yellow-20, $color-yellow-alpha10);
  }
  &--low {
    @include c-badge($color-blue, $color-blue-alpha10);
  }
  &--safe {
    @include c-badge($color-green, $color-green-alpha10);
  }
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-badge {
    font-size: 10px;
    padding: 2px 8px;
    min-height: 20px;
  }
}
