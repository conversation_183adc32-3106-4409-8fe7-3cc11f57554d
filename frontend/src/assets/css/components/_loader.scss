@charset "utf-8";

@use '../global/variables' as *;
@use '../global/animation' as *;

/* loadingアイコン */
.c-loader {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  background: conic-gradient($color-gradation);
  animation: animation-rotate 1s linear infinite;
  &::before {
    content: '';
    position: absolute;
    box-sizing: border-box;
    width: 30px;
    height: 30px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: $color-white;
    border-radius: 100%;
  }
  &--button {
    width: 20px;
    height: 20px;
    &::before {
      width: 12px;
      height: 12px;
      background: $color-gray20;
    }
  }
}
.c-loaderChat {
  display: inline-block;
  & span {
    width: 8px;
    height: 8px;
    margin: 2px;
    display: inline-block;
    background-color: $color-darkGreen;
    border-radius: 100%;
    animation: animation-ballBeat 0.7s 0s infinite linear;
    &:nth-child(2n-1) {
      animation-delay: -0.35s !important;
    }
  }
}

.c-loaderContetnt {
  display: grid;
  row-gap: 20px;
  justify-content: center;
  text-align: center;
  &__icon {
    display: flex;
    justify-content: center;
  }
  &__text {
    color: $color-darkGreen;
  }
  &__subText {
    display: block;
    font-size: 12px;
    color: $color-gray80;
    margin-top: 8px;
  }
  &__note {
    font-size: 12px;
    color: $color-gray80;
  }
}
/* margin */
.c-panel__title + .c-loaderContetnt {
  margin-bottom: 24px;
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-loaderContetnt {
    row-gap: 0;
    &__text {
      margin-top: 20px;
    }
    &__note {
      margin-top: 10px;
      margin-bottom: 20px;
    }
  }
}
