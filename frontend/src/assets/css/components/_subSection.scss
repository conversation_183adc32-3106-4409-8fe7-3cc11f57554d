@charset "utf-8";

@use '../global/variables' as *;

.c-subSection {
  border-radius: 20px;
  background: url('https://security-api.gmo.jp/static/img/bg_parts_2.jpg') top center / cover;
  box-shadow: $shadow-10;
  max-width: 1400px;
  margin: 0 auto;
  &__inner {
    max-width: 1040px;
    margin: 0 auto;
    padding: 20px 60px 48px;
  }
  &__text {
    font-size: 20px;
    text-align: center;
    color: $color-white;
  }
  &__form {
    margin-top: 24px;
    margin-bottom: 64px;
  }
  &__note {
    font-size: 12px;
    color: $color-white;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-inline: auto;
    max-inline-size: max-content;
  }
  &__img {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-subSection {
    font-size: 16px;
    padding: 0 0 20px 0;
    background: url('https://security-api.gmo.jp/static/img/bg_parts_2_sp.jpg') top center / cover;
    &__inner {
      max-width: 100%;
      padding: 0 15px;
    }
    &__form {
      margin-top: 10px;
      margin-bottom: 40px;
    }
    &__text {
      font-size: 16px;
      text-align: center;
      color: $color-white;
      & span {
        display: block;
        & br {
          display: none;
        }
      }
    }
    &__img {
      & img {
        width: 100%;
        height: auto;
      }
    }
  }
}
