@charset "utf-8";

@use '../global/variables' as *;
@use '../global/animation' as *;

.c-siteRiskPeriodicCheckup {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: $color-white;
  border-top: 1px solid $color-gray20;
  padding-top: 20px;
  &__textWrap {
    display: flex;
    column-gap: 8px;
    margin-right: auto;
  }
  &__icon {
    margin-top: 2px;
  }
  &__text {
    font-weight: 600;
    font-size: 16px;
    color: $color-darkGreen;
  }
  &__list {
    display: inline-flex;
    align-items: center;
    column-gap: 8px;
  }
  &__date {
    font-size: 12px;
    color: $color-darkGreen;
  }
}
.c-siteRiskPeriodicCheckupElement {
  padding: 4px 8px;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card-half;
  display: flex;
  align-items: center;
  column-gap: 10px;
  & span {
    font-size: 14px;
  }
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-siteRiskPeriodicCheckup {
    display: grid;
    row-gap: 10px;
    &__list {
      display: grid;
      row-gap: 10px;
    }
  }
  .c-siteRiskPeriodicCheckupElement {
    & span {
      margin-right: auto;
    }
  }
}
