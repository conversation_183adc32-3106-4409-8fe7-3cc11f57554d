@charset "utf-8";

@use '../global/variables' as *;

.p-verify {
  &__attention {
    display: flex;
    column-gap: 8px;
    color: $color-darkGreen;
    margin-top: 20px;
    & span {
      margin-top: 4px;
    }
  }
  &__agreement {
    background-color: $color-darkGreen-alpha5;
    padding: 20px;
    border-radius: $border-radius-card;
    display: grid;
    justify-content: center;
    margin-top: 40px;
  }
  &__label {
    display: flex;
    align-items: center;
    column-gap: 8px;
    color: $color-darkGreen;
    cursor: pointer;
    font-weight: 600;
  }
  &__button {
    width: 240px;
    margin: 40px auto 0;
  }
  &__note {
    font-size: 12px;
    color: $color-gray80;
    margin-top: 20px;
  }
}
.p-verifyStep {
  display: grid;
  row-gap: 10px;
  & li {
    background-color: $color-gray10;
    border-radius: $border-radius-card;
    padding: 20px;
    display: grid;
    row-gap: 20px;
  }
  &__title {
    font-size: 16px;
    color: $color-black;
    display: flex;
    column-gap: 10px;
    @mixin p-verifyStep-before($content) {
      &::before {
        content: $content;
        display: inline-flex;
        justify-content: center;
        line-height: 1;
        align-items: center;
        font-size: 14px;
        width: 24px;
        height: 24px;
        color: $color-white;
        background-color: $color-darkGreen;
        border-radius: $border-radius-round;
        flex-shrink: 0;
      }
    }
    &--step01 {
      @include p-verifyStep-before('1');
    }
    &--step02 {
      @include p-verifyStep-before('2');
    }
    &--step03 {
      @include p-verifyStep-before('3');
    }
  }
  & p {
    padding-left: 32px;
  }
}
.p-verifyStepContent {
  display: grid;
  grid-template-columns: 100px 1fr;
  align-items: center;
  row-gap: 20px;
  & dd {
    display: flex;
    justify-content: space-between;
    background-color: $color-white;
    border-radius: $border-radius-card;
    padding: 12px 20px;
    & code {
      overflow-wrap: anywhere;
      word-break: break-word;
    }
  }
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .p-verify {
    &__attention {
      font-size: 14px;
      column-gap: 4px;
    }
    &__button {
      width: 100%;
      margin-bottom: 40px;
    }
  }
  .p-verifyStepContent {
    grid-template-columns: inherit;
    row-gap: 8px;
    & dt {
      font-size: 12px;
    }
  }
  .p-verifyStep {
    &__title {
      column-gap: 8px;
    }
    & li {
      row-gap: 10px;
    }
    & p {
      font-size: 12px;
    }
  }
}
