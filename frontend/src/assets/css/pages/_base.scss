@charset "utf-8";

@use "../global/variables" as *;

.p-base {
  position: relative;
  background-color: $color-gray10;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 60%;
    aspect-ratio: 100 / 67;
    background: url("https://security-api.gmo.jp/static/img/bg_parts_1.png") top
      right / contain no-repeat;
    opacity: 0.3;
  }
  padding-bottom: 120px;
  &__inner {
    position: relative; /* 背景画像の前後調整 */
    max-width: 1040px;
    margin: 0 auto;
  }
  &__sub {
    position: relative; /* 背景画像の前後調整 */
    margin-top: 120px;
  }
  &__scroll {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
  &__list {
    display: grid;
    row-gap: 40px;
  }
  &__text {
    color: $color-darkGreen;
    text-align: center;
    margin-bottom: 40px;
  }
  &__message {
    padding-top: 80px;
    margin-bottom: 20px;
  }
}
.p-baseFixed {
  position: sticky;
  right: 40px;
  bottom: 0;
  z-index: $z-index-1;
  &__contactButton {
    position: absolute;
    right: 40px;
    bottom: 110px;
    z-index: $z-index-1;
  }
  &__chatButton {
    position: absolute;
    right: 40px;
    bottom: 40px;
    z-index: $z-index-1;
  }
}
.p-baseChat {
  &__board {
    padding: 10px 10px 48px 10px;
    border-radius: $border-radius-card;
    background: $color-white;
    box-shadow: $shadow-20;
    width: 375px;
    height: 460px;
  }
}

.p-baseContact {
  width: 140px;
}

/* パララックス */
.p-parallaxObj {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.7s, transform 0.7s;

  &.is-already {
    opacity: 1;
    transform: translateY(0);
  }
}

/* margin */
.p-base__list + .p-base__contact {
  margin-top: 120px;
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .p-base {
    padding-bottom: 100px;
    &::before {
      width: 100%;
      opacity: 0.4;
    }
    &__inner {
      padding: 0 15px;
    }
    &__sub {
      margin-top: 60px;
      margin-left: 15px;
      margin-right: 15px;
    }
    &__text {
      font-size: 10px;
    }
    &__message {
      padding-top: 60px;
    }
  }
  .p-baseFixed {
    position: sticky;
    right: 15px;
    bottom: 0;
    z-index: $z-index-1;
    &__contactButton {
      right: 15px;
      bottom: 80px;
    }
    &__chatButton {
      right: 15px;
      bottom: 15px;
    }
  }
  .p-baseChat {
    &__board {
      width: 300px;
      height: 380px;
    }
  }
  .p-baseContact {
    width: 100px;
  }
}
