@charset "utf-8";

/* 呼び出し元のwpコンテンツのcommonを使用、一部編集 */
@use "variables" as *;
@use "mixin" as *;

* {
  box-sizing: border-box;
}

html,
body {
  color: $color-black;
  font-family: $font-family;
  font-size: 16px;
  line-height: 1.58;
  margin: 0;
  padding: 0;
  width: 100%;
}

body {
  background: $color-gray10;

  // カテゴリごとのグラデーションカラー
  // --default_grad: linear-gradient(0deg, #005bac 0%, #4978bd 5%, #e3b6e0 40%, #f1ede5 60%, #f6f6f6 100%);
  --default_grad: linear-gradient(
    0deg,
    #f6f6f6 0%,
    #f1ede5,
    67%,
    #e3b6e0 75%,
    #4978bd 95%,
    #005bac 100%
  );
  --category_grad_all: linear-gradient(180deg, #0f3558, #005bac);
  --category_grad_cipher: linear-gradient(180deg, #af579e, #005bac);
  --category_grad_cyber: linear-gradient(180deg, #009abf, #005bac);
  --category_grad_brand: linear-gradient(180deg, #68c76f, #005bac);
  --category_grad_all_90: linear-gradient(90deg, #0f3558, #005bac);
  --category_grad_cipher_90: linear-gradient(90deg, #af579e, #005bac);
  --category_grad_cyber_90: linear-gradient(90deg, #009abf, #005bac);
  --category_grad_brand_90: linear-gradient(90deg, #68c76f, #005bac);
}

h4,
h5,
h6,
p,
dt,
dd,
li {
  font-size: 16px;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
dt,
dd {
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 dt {
  font-weight: 400;
}

a {
  color: $color-black;
  font-size: 16px;
  text-decoration: none;

  &:focus,
  &:hover {
    color: $color-gmo;
    text-decoration: underline;
  }

  img {
    border: none;
  }
}

img {
  height: auto;
  max-width: 100%;
  vertical-align: middle;
}

br {
  line-height: 0;
}

strong {
  font-weight: 400;
}

ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

dl,
dt,
dd {
  margin: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  padding: 0;
}

th,
td {
  font-size: 16px;
  padding: 0;

  p,
  li,
  dt,
  dd {
    font-size: inherit;
  }
}

input,
textarea {
  font-family: $font-family;
  font-size: 16px;
  outline: 0;
}

button {
  background: none;
  border: none;
  color: $color-black;
  cursor: pointer;
  display: block;
  font-size: 16px;
  /* outline: none; */
  padding: 0;
}

details {
  list-style: none;
}

details summary {
  list-style: none;
}

/* 追記 ちらつき防止 */
#app {
  min-height: 696px;
}
main.main-top #app {
  background-color: $color-white;
}

/* 追記 reCAPTCHA 削除 */
.grecaptcha-badge {
  display: none !important;
}

/* 追記 アンカー位置の調整 */
#anchorLink_nds,
#anchorLink_cloud,
#anchorLink_ssl,
#anchorLink_impersonation {
  padding-top: 76px;
  margin-top: -76px;
}
#anchorLink_contact {
  padding-top: 40px;
  margin-top: -40px;
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  #app {
    min-height: 512px;
  }
  /* 追記 アンカー位置の調整 */
  #anchorLink_nds,
  #anchorLink_cloud,
  #anchorLink_ssl,
  #anchorLink_impersonation {
    padding-top: 62px;
    margin-top: -62px;
  }
  #anchorLink_contact {
    padding-top: 20px;
    margin-top: -20px;
  }
}
