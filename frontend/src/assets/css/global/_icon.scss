@use "../global/variables" as *;

@font-face {
  font-display: swap;
  font-family: "security-icon";
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/security-icon.eot");
  src: url("../fonts/security-icon.eot#iefix") format("eot"),
    url("../fonts/security-icon.ttf") format("woff"),
    url("../fonts/security-icon.woff") format("truetype"),
    url("../fonts/security-icon.svg#security-icon") format("svg");
}

.icon-base,
[class*=" icon-sec-"] {
  font-family: "security-icon" !important;
  font-size: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  speak: none;
  text-decoration: none;
  text-transform: none;
}

.icon-sec-attention::before {
  content: "\e900";
}
.icon-sec-caution::before {
  content: "\e901";
}
.icon-sec-chat::before {
  content: "\e902";
}
.icon-sec-chatbubble::before {
  content: "\e90e";
}
.icon-sec-checkBox::before {
  content: "\e903";
}
.icon-sec-close::before {
  content: "\e904";
}
.icon-sec-cloud::before {
  content: "\e905";
}
.icon-sec-copy::before {
  content: "\e906";
}
.icon-sec-expansion::before {
  content: "\e907";
}
.icon-sec-impersonation::before {
  content: "\e908";
}
.icon-sec-link::before {
  content: "\e909";
}
.icon-sec-operator::before {
  content: "\e90a";
}
.icon-sec-search::before {
  content: "\e90f";
}
.icon-sec-security::before {
  content: "\e90b";
}
.icon-sec-send::before {
  content: "\e90c";
}
.icon-sec-ssl::before {
  content: "\e90d";
}
.icon-sec-good::before {
  content: "\e910";
}
.icon-sec-good-on::before {
  content: "\e911";
}
.icon-sec-question::before {
  content: "\e912";
}
.icon-sec-error::before {
  content: "\e913";
}
.icon-sec-cycle::before {
  content: "\e914";
}
.icon-sec-www::before {
  content: "\e915";
}
.icon-sec-other::before {
  content: "\e916";
}
.icon-sec-chatbubble-bold::before {
  content: "\e917";
}
.icon-sec-search-bold::before {
  content: "\e918";
}

/* サイズ */
.icon-size40 {
  font-size: 40px;
}
.icon-size36 {
  font-size: 36px;
}
.icon-size24 {
  font-size: 24px;
}
.icon-size20 {
  font-size: 20px;
}
.icon-size16 {
  font-size: 16px;
}
.icon-size14 {
  font-size: 14px;
}
.icon-size12 {
  font-size: 12px;
}
.icon-size10 {
  font-size: 10px;
}

/* 色 */
.icon-color-black {
  color: $color-black;
}
.icon-color-white {
  color: $color-white;
}
.icon-color-green {
  color: $color-green;
}
.icon-color-darkGreen {
  color: $color-darkGreen;
}
.icon-color-orange {
  color: $color-orange;
}
.icon-color-gray40 {
  color: $color-gray40;
}
.icon-color-gray70 {
  color: $color-gray70;
}

/*　回転 */
.icon-rotate180 {
  transform: rotate(180deg);
}
