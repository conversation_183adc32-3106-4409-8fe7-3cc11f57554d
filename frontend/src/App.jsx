import { BrowserRouter, Route, Routes } from 'react-router-dom';
import Chat from './apps/chat/Chat';
import Complete from './apps/complete/Complete';
import Confirm from './apps/confirm/Confirm';
import Email from './apps/email/Email';
import Home from './apps/home/<USER>';
import Password from './apps/password/Password';
import SiteRisk from './apps/site-risk/SiteRisk';
import Verify from './apps/verify/Verify';
import Yourbrand from './apps/yourbrand/Yourbrand';
import YourbrandSearch from './apps/yourbrand/YourbrandSearch';

function App() {
  return (
    <>
      <BrowserRouter>
        <Routes>
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/`}
            element={<Home />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/complete/`}
            element={<Complete />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/confirm/`}
            element={<Confirm />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/email/`}
            element={<Email />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/verify/`}
            element={<Verify />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/password/`}
            element={<Password />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/site-risk/`}
            element={<SiteRisk />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/check/chat/`}
            element={<Chat />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/yourbrand/`}
            element={<Yourbrand />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/yourbrand/search/`}
            element={<YourbrandSearch />}
          />
          <Route
            path={`${import.meta.env.VITE_PATH_PREFIX}/*`}
            element={<h1>Not Found Page</h1>}
          />
        </Routes>
      </BrowserRouter>
    </>
  );
}

export default App;
