import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css';
import './assets/css/style.scss';
import './assets/js/index.js';
import App from './App.jsx';

const WrappedApp = () => {
  if (import.meta.env.VITE_ENV === 'local') {
    return <App />;
  }
  return (
    <StrictMode>
      <App />
    </StrictMode>
  );
};

createRoot(document.getElementById('app')).render(<WrappedApp />);
