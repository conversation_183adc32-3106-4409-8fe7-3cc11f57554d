import { PubSub } from '@google-cloud/pubsub';
import { SLACK_TEMPLATE_BRAND_TLD_CONTACT, SLACK_TEMPLATE_PASSWORD_CONTACT, SLACK_TEMPLATE_SITE_RISK_CONTACT } from '../constants/slack_template.js';
import {
  TEMPLATE_PASSWORD_CONTACT_THANKS,
  TEMPLATE_NDS_CONTACT_THANKS,
  TEMPLATE_CLOUD_CONTACT_THANKS,
  TEMPLATE_IMPERSONATION_CONTACT_THANKS,
  TEMPLATE_SSL_CONTACT_THANKS,
  TEMPLATE_BRAND_TLD_CONTACT_THANKS,
} from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_SPREADSHEET } from '../constants/topics.js';
import { decrypt } from '../services/cryptography.js';
import { convertUTCtoJST } from '../services/daytime.js';
import { isVerified } from '../services/recaptcha.js';
import { createBrandTldContactSink, createPasswordContactSink, createSiteRiskContactSink } from '../services/sink.js';

const TARGETS = {
  nds: 'Webサイト脆弱性診断',
  cloud: 'クラウド利用・リスク診断',
  ssl: '実在証明・盗聴防止（SSL）診断',
  impersonation: 'なりすまし診断',
  brand_tld: '.貴社名',
};
const TARGET_REACTIONS = {
  nds: 'nds',
  cloud: 's-cloud',
  ssl: 'ssl',
  impersonation: 'impersonation',
  password: 'password',
  brand_tld: 'impersonation',
};
const SHEET_IDS = {
  nds: process.env.NDS_SPREADSHEET_SHEET_ID,
  ssl: process.env.SSL_SPREADSHEET_SHEET_ID,
  cloud: process.env.CLOUD_SPREADSHEET_SHEET_ID,
  impersonation: process.env.IMPERSONATION_SPREADSHEET_SHEET_ID,
  brand_tld: process.env.BRAND_TLD_SPREADSHEET_SHEET_ID,
};

const MAIL_TEMPLATES = {
  nds: TEMPLATE_NDS_CONTACT_THANKS,
  cloud: TEMPLATE_CLOUD_CONTACT_THANKS,
  ssl: TEMPLATE_SSL_CONTACT_THANKS,
  impersonation: TEMPLATE_IMPERSONATION_CONTACT_THANKS,
  password: TEMPLATE_PASSWORD_CONTACT_THANKS,
  brand_tld: TEMPLATE_BRAND_TLD_CONTACT_THANKS,
};

const SLACK_MESSAGE_CHANNEL_IDS = {
  nds: process.env.SLACK_FQDN_CHANNEL_ID,
  cloud: process.env.SLACK_FQDN_CHANNEL_ID,
  impersonation: process.env.SLACK_FQDN_CHANNEL_ID,
  ssl: process.env.SLACK_FQDN_CHANNEL_ID,
  password: process.env.SLACK_PASSWORD_CONTACT_CHANNEL_ID,
  brand_tld: process.env.SLACK_BRAND_TLD_CONTACT_CHANNEL_ID,
};

const SLACK_MESSAGE_TEMPLATES = {
  nds: SLACK_TEMPLATE_SITE_RISK_CONTACT,
  cloud: SLACK_TEMPLATE_SITE_RISK_CONTACT,
  impersonation: SLACK_TEMPLATE_SITE_RISK_CONTACT,
  ssl: SLACK_TEMPLATE_SITE_RISK_CONTACT,
  password: SLACK_TEMPLATE_PASSWORD_CONTACT,
  brand_tld: SLACK_TEMPLATE_BRAND_TLD_CONTACT,
};

const schema = {
  body: {
    type: 'object',
    required: ['email', 'recaptchaToken', 'targets'],
    properties: {
      code: { type: 'string' },
      company: { type: 'string' },
      fullname: { type: 'string' },
      email: { type: 'string', format: 'email' },
      targets: {
        type: 'array',
        minItems: 1,
        maxItems: 1,
        items: { type: 'string', enum: ['nds', 'cloud', 'ssl', 'impersonation', 'password', 'brand_tld'] },
      },
      telephone: {
        type: 'string',
        pattern: '^(0\\d{1,4}-?\\d{1,4}-?\\d{3,4}|0\\d{9,10})$',
      },
      consultation: { type: 'string' },
      threadTs: { type: 'string' },
      recaptchaToken: { type: 'string' },
      tld: { type: 'string', minBytes: 3 },
    },
    allOf: [
      {
        if: { properties: { targets: { type: 'array', contains: { const: 'brand_tld' } } } },
        then: { required: ['company', 'fullname', 'telephone'] },
      },
      {
        if: { properties: { targets: { type: 'array', contains: { const: 'ssl' } } } },
        then: { required: ['code', 'fullname', 'telephone'] },
      },
      {
        if: { properties: { targets: { type: 'array', contains: { const: 'password' } } } },
        then: { required: ['code', 'fullname'] },
      },
      {
        if: {
          properties: {
            targets: {
              type: 'array', anyOf: [
                { contains: { const: 'nds' } },
                { contains: { const: 'cloud' } },
                { contains: { const: 'impersonation' } },
              ],
            },
          },
        },
        then: { required: ['code'] },
      },
    ],
  },
};

const pubSubClient = new PubSub();

const handler = async (request, reply) => {
  const {
    code,
    company = '',
    fullname = '',
    email,
    targets = [],
    telephone = '',
    consultation = '',
    threadTs = '1740115413.026959',
    recaptchaToken,
    tld = '',
  } = request.body;

  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  const contactedAt = new Date();
  const target = targets[0];

  let fqdn;

  if (code) {
    const decrypted = await decrypt(
      code,
      process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
      process.env.SECRET_CRYPTOGRAPHY_SALT,
    );
    if (!decrypted) {
      // eslint-disable-next-line no-console
      console.log(`decrypted is null code: ${code}`);
      return reply.status(400).send({ status: 'error', message: 'Invalid code' });
    }

    const { email: codedEmail, fqdn: decodedFqdn } = JSON.parse(decrypted);
    fqdn = decodedFqdn;
    if (!codedEmail) {
      return reply.status(400).send({ status: 'error', message: 'Invalid code' });
    }
  }

  if (fqdn) {
    await createSiteRiskContactSink({ email, code, fqdn, targets, consultation, threadTs });
  } else {
    if (target == 'brand_tld') {
      await createBrandTldContactSink({ email, tld });
    } else {
      await createPasswordContactSink(email, code, consultation);
    }
  }

  await sendMailEvent({ target, email, fullname, consultation, contactedAt });
  await sendSlackMessage({ target, email, fullname, telephone, company, consultation, code, contactedAt, threadTs, tld });
  await createSpreadSheetData({ target, email, fullname, telephone, company, fqdn, contactedAt, threadTs, tld });

  return reply.send({ status: 'success' });
};

const createSpreadSheetData = async ({ target, contactedAt, email, fqdn, fullname, telephone, company, threadTs, tld }) => {
  if (target == 'password') {
    return;
  }

  const sheetId = SHEET_IDS[target];
  const channelId = SLACK_MESSAGE_CHANNEL_IDS[target];

  const threadUrl = `https://gmointernetworkspace.slack.com/archives/${channelId}/p${threadTs.replace('.', '')}`;
  const constactedAtJST = convertUTCtoJST(contactedAt);
  const docId = process.env.CONTACT_SPREADSHEET_DOC_ID;

  const topic = pubSubClient.topic(TOPIC_WRITE_SPREADSHEET);
  const message = Buffer.from(JSON.stringify({
    docId,
    sheetId: sheetId,
    records: [[constactedAtJST, email, fqdn ?? tld, `${company}${!!company ? ' ' : ''}${fullname}`, telephone, threadUrl]],
  }));
  await topic.publishMessage({ data: message });
};

const sendMailEvent = async ({ target, email, fullname, consultation, contactedAt }) => {
  const template = MAIL_TEMPLATES[target];
  const convertedTarget = TARGETS[target];
  const topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
  const message = Buffer.from(
    JSON.stringify({
      email,
      template: template,
      params: { email, fullname, targets: [convertedTarget], consultation, contactedAt },
    }),
  );
  await topic.publishMessage({ data: message });
};

const sendSlackMessage = async ({ code, email, fullname, telephone, target, company, consultation, contactedAt, threadTs, tld }) => {
  const channelId = SLACK_MESSAGE_CHANNEL_IDS[target];
  const template = SLACK_MESSAGE_TEMPLATES[target];

  const reaction = TARGET_REACTIONS[target];
  const convertedTarget = TARGETS[target];

  let replyBroadcast = false;
  if (target !== 'password' && target !== 'brand_tld') {
    replyBroadcast = true;
  }

  const topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);
  const token = process.env.SECRET_SLACK_USER_TOKEN;
  const message = Buffer.from(
    JSON.stringify({
      token,
      channelId,
      message: {
        template: template,
        params: { code, email, fullname, telephone, targets: [convertedTarget], company, consultation, contactedAt, tld },
      },
      threadTs,
      replyBroadcast,
      reactions: [reaction],
    }),
  );

  await topic.publishMessage({ data: message });
};

export default { schema, handler };
