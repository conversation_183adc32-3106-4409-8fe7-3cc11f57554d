import { PubSub } from '@google-cloud/pubsub';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { TOPIC_CHECK_EMAIL } from '../constants/topics.js';
import { encrypt } from '../services/cryptography.js';
import { isVerified } from '../services/recaptcha.js';

const schema = {
  body: {
    type: 'object',
    required: ['email', 'recaptchaToken'],
    properties: {
      email: {
        type: 'string',
        format: 'email',
      },
      recaptchaToken: { type: 'string' },
    },
  },
};

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

export const publishCheckEmail = async (email, isRegularly = false) => {
  const createdAt = new Date();
  const expiredAt = new Date(createdAt.getTime() + TTL_MILLISECOND);

  const code = await encrypt(
    JSON.stringify({ email, expiredAt: expiredAt.getTime() }),
    process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
    process.env.SECRET_CRYPTOGRAPHY_SALT);

  const topic = pubSubClient.topic(TOPIC_CHECK_EMAIL);
  const data = Buffer.from(JSON.stringify({ email, createdAt, expiredAt, code, isRegularly }));

  await topic.publishMessage({ data });
};

const handler = async (request, reply) => {
  const { email, recaptchaToken } = request.body;

  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  await publishCheckEmail(email);

  return reply.send({ status: 'success' });
};

export default { schema, handler };
