import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, COLLECTION_USER_CONFIGURATION } from '../constants/collections.js';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { getDoc, saveDoc } from '../providers/firestore.js';
import { decrypt, hash, hashEmail, priDecrypt, pubEncrypt } from '../services/cryptography.js';
import { createPasswordConfigurationSink, createSiteRiskConfigurationSink } from '../services/sink.js';

const schema = {
  body: {
    type: 'object',
    required: ['code'],
    properties: {
      code: { type: 'string' },
      isRegularly: { type: 'boolean' },
      interval: {
        type: 'integer',
        minimum: 1,
        maximum: 6,
      },
      isNotification: { type: 'boolean' },
    },
  },
};

const handler = async (request, reply) => {
  const { code, isRegularly, interval, isNotification } = request.body;

  const decrypted = await decrypt(code, process.env.SECRET_CRYPTOGRAPHY_PASSWORD, process.env.SECRET_CRYPTOGRAPHY_SALT);
  if (!decrypted) {
    console.log(`decrypted is null code: ${code}`);
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const { email, fqdn, expiredAt } = JSON.parse(decrypted);
  if (!email) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const checkedAt = new Date((new Date(expiredAt)).getTime() - TTL_MILLISECOND);
  const encryptedEmail = await pubEncrypt(email, process.env.SECRET_PUBLIC_KEY);
  const hashedEmail = await hashEmail(email);

  if (isNotification !== undefined) {
    const configId = hashedEmail;
    const configDoc = await getDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId });
    if (configDoc.exists) {
      await configDoc.ref.update({ [!!fqdn ? 'isSiteRiskNotification' : 'isPasswordNotification']: isNotification });
    } else {
      const config = { encryptedEmail, isSiteRiskNotification: false, isPasswordNotification: false };
      if (fqdn) {
        config.isSiteRiskNotification = isNotification;
      } else {
        config.isPasswordNotification = isNotification;
      }
      await saveDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId, data: config });
    }
  }

  if (isRegularly !== undefined && interval !== undefined) {
    let nextCheckedAt = null;
    if (isRegularly) {
      nextCheckedAt = new Date(checkedAt);
      nextCheckedAt.setMonth(checkedAt.getMonth() + interval);
    }

    if (fqdn) {
      const siteRiskId = await hash(`${email}:${fqdn}`);
      const siteRiskDoc = await getDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId });

      if (siteRiskDoc.exists) {
        await siteRiskDoc.ref.update({ isRegularly, interval, nextCheckedAt: nextCheckedAt ? nextCheckedAt.toISOString() : null });
      } else {
        const siteRisk = { encryptedEmail, fqdn, isRegularly, interval, nextCheckedAt: nextCheckedAt ? nextCheckedAt.toISOString() : null, history: [] };
        await saveDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId, data: siteRisk });
      }

      await createSiteRiskConfigurationSink(email, fqdn, isRegularly, interval, nextCheckedAt);
    } else {
      const passwordId = hashedEmail;
      const passwordDoc = await getDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId });

      if (passwordDoc.exists) {
        await passwordDoc.ref.update({ isRegularly, interval, nextCheckedAt: nextCheckedAt ? nextCheckedAt.toISOString() : null });
      } else {
        const password = { encryptedEmail, isRegularly, interval, nextCheckedAt: nextCheckedAt ? nextCheckedAt.toISOString() : null, history: [] };
        await saveDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId, data: password });
      }

      await createPasswordConfigurationSink(email, isRegularly, interval, nextCheckedAt);
    }
  }

  return reply.send({ status: 'success' });
};

export default { schema, handler };
