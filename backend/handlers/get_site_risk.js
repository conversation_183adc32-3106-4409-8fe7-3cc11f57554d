import {
  COLLECTION_CLOUD,
  COLLECTION_IMPERSONATION,
  COLLECTION_NDS,
  COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION,
  COLLECTION_SSL,
} from '../constants/collections.js';
import { THIRTY_DAYS_MILLISECOND, TTL_MILLISECOND } from '../constants/constants.js';
import { getDoc } from '../providers/firestore.js';
import { decrypt, hash, hashEmail } from '../services/cryptography.js';
import { convertDate, convertUTCtoJST } from '../services/daytime.js';

const schema = {
  querystring: {
    type: 'object',
    required: ['code'],
    properties: {
      code: {
        type: 'string',
        pattern: '^[a-z0-9-]+$',
      },
    },
  },
};

const calculateSslOverview = ({ hostnameVerification, certVerification, freeSslProvider, earliestExpires }) => {
  let status = 'safe';
  let text = '安全です';
  if (!hostnameVerification) {
    status = 'alert';
    text = '不一致';
  } else if (!certVerification) {
    status = 'alert';
    text = '無効';
  } else {
    const now = new Date();
    const after30Days = new Date(now.getTime() + THIRTY_DAYS_MILLISECOND);

    if (!!freeSslProvider) {
      if (new Date(earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        text = '変更推奨';
      } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
        status = 'warning';
        text = '変更推奨';
      } else {
        status = 'warning';
        text = '変更推奨';
      }
    } else {
      if (new Date(earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        text = '変更推奨';
      } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
        status = 'warning';
        text = '変更推奨';
      } else {
        status = 'safe';
        text = '安全です';
      }
    }
  }
  return { status, text };
};

const handler = async (request, reply) => {
  const { code } = request.query;

  const decrypted = await decrypt(code, process.env.SECRET_CRYPTOGRAPHY_PASSWORD, process.env.SECRET_CRYPTOGRAPHY_SALT);
  if (!decrypted) {
    console.log(`decrypted is null code: ${code}`);
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const { email, fqdn, expiredAt } = JSON.parse(decrypted);
  if (!email || !fqdn || !expiredAt) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const siteRiskId = await hash(`${email}:${fqdn}`);

  const [ndsDoc, cloudDoc, sslDoc, impersonationDoc, siteRiskDoc] = await Promise.all([
    getDoc({ collection: COLLECTION_NDS, docId: code }),
    getDoc({ collection: COLLECTION_CLOUD, docId: code }),
    getDoc({ collection: COLLECTION_SSL, docId: code }),
    getDoc({ collection: COLLECTION_IMPERSONATION, docId: code }),
    getDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId }),
  ]);

  if (!siteRiskDoc.exists) {
    return reply.status(404).send({ status: 'error', message: 'Not found' });
  }

  const { isRegularly, interval, nextCheckedAt, history } = siteRiskDoc.data();
  const configuration = { isRegularly, interval, nextCheckedAt: nextCheckedAt ? convertUTCtoJST(nextCheckedAt) : null, isNotification: false };

  const overview = {
    email,
    fqdn,
    createdAt: null,
    nds: { status: 'processing' },
    cloud: { status: 'processing' },
    ssl: { status: 'processing' },
    impersonation: { status: 'processing' },
  };

  const now = new Date();
  if (expiredAt < now.getTime() || (!ndsDoc.exists && !cloudDoc.exists && !sslDoc.exists && !impersonationDoc.exists)) {
    const result = history?.find(h => code === h.code);
    if (result) {
      overview.nds = result.nds.status === 'error' ? { status: 'error' } : { status: 'rank', rank: result.nds.rank };
      const isCloudUsed = result.cloud.providers.length > 0;
      overview.cloud = result.cloud.status === 'error' ? { status: 'error' } : { status: isCloudUsed ? 'warning' : 'cloud', isUsed: isCloudUsed, text: isCloudUsed ? '要確認' : '利用なし' };
      overview.ssl = result.ssl.status === 'error' ? { status: 'error' } : calculateSslOverview(result.ssl);
      overview.impersonation = result.impersonation.status === 'error' ? { status: 'error' } : { status: 'rank', rank: result.impersonation.rank };
      overview.createdAt = result.nds.createdAt;
    }
    return reply.send({ status: 'success', message: null, result: { configuration, overview } });
  }

  let createdAt = null;
  let nds;
  if (ndsDoc.exists) {
    const data = ndsDoc.data();
    if (data.result) {
      createdAt = data.createdAt;

      if (data.result.status === 'error') {
        overview.nds = { status: 'error' };
        nds = { status: 'error' };
      } else {
        const levelCounts = { info: 0, low: 0, medium: 0, high: 0, critical: 0 };
        const details = data.result.result.items
          .sort((a, b) => b.cvss - a.cvss)
          .map((r) => {
            let level = 'info';
            if (r.cvss >= 9.5) level = 'critical';
            else if (r.cvss >= 8) level = 'high';
            else if (r.cvss >= 5.5) level = 'medium';
            else if (r.cvss >= 2) level = 'low';
            return { ...r, level };
          })
          .map((r) => {
            levelCounts[r.level]++;
            return { title: r.title, impact: r.impact, measure: r.measure, level: r.level };
          });
        const summary = { assessmentTime: data.result.assessment_time, rank: data.result.rank, levelCounts };
        nds = { status: 'success', summary, details };

        overview.nds = { status: 'rank', rank: data.result.rank };
        overview.threadTs = data.threadTs;
      }
    }
  }

  let cloud;
  if (cloudDoc.exists) {
    const data = cloudDoc.data();
    if (data.result) {
      if (!createdAt) createdAt = data.createdAt;

      if (data.status && data.status === 'error') {
        overview.cloud = { status: 'error' };
        cloud = { status: 'error' };
      } else {
        const details = Array.from(new Set(data.result.filter(r => !!r.provider).map(r => JSON.stringify({ cloud: r.provider })))).map(JSON.parse);
        const isUsed = details.reduce((pre, cur) => pre || ['AWS', 'GoogleCloud', 'Azure'].includes(cur.cloud), false);
        const summary = { createdAt: data.createdAt, isUsed };
        cloud = { status: 'success', summary, details };

        overview.cloud = { status: isUsed ? 'warning' : 'cloud', isUsed, text: isUsed ? '要確認' : '利用なし' };
      }
    }
  }

  let ssl;
  if (sslDoc.exists) {
    const data = sslDoc.data();
    if (!createdAt) createdAt = data.createdAt;

    if (data.status && data.status === 'error') {
      overview.ssl = { status: 'error' };
      ssl = { status: 'error' };
    } else {
      const details = Array.from(data.result.map(r => JSON.stringify({ freeSslProvider: r.free_ssl_provider, expires: r.validity?.not_after ? (convertDate(r.validity.not_after)).toISOString() : null, hostnameVerification: r.hostname_verification, certVerification: r.cert_verification }))).map(JSON.parse);

      const hostnameVerification = details.find(r => !!r.hostnameVerification)?.hostnameVerification?.verified;
      const certVerification = details.find(r => !!r.certVerification)?.certVerification?.verified;
      let freeSslProvider = null;
      let earliestExpires = null;

      let status = 'safe';
      let text = '安全です';
      if (!hostnameVerification) {
        status = 'alert';
        text = '不一致';
      } else if (!certVerification) {
        status = 'alert';
        text = '無効';
      } else {
        freeSslProvider = details.find(r => !!r.freeSslProvider)?.freeSslProvider;
        earliestExpires = details.filter(({ expires }) => expires).map(({ expires }) => expires).sort((a, b) => new Date(a).getTime() - new Date(b).getTime())[0];

        const now = new Date();
        const after30Days = new Date(now.getTime() + THIRTY_DAYS_MILLISECOND);

        if (!!freeSslProvider) {
          if (new Date(earliestExpires).getTime() < now.getTime()) {
            status = 'alert';
            text = '変更推奨';
          } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
            status = 'warning';
            text = '変更推奨';
          } else {
            status = 'warning';
            text = '変更推奨';
          }
        } else {
          if (new Date(earliestExpires).getTime() < now.getTime()) {
            status = 'alert';
            text = '変更推奨';
          } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
            status = 'warning';
            text = '変更推奨';
          } else {
            status = 'safe';
            text = '安全です';
          }
        }
      }
      const summary = { createdAt: data.createdAt, freeSslProvider, earliestExpires, hostnameVerification, certVerification };
      ssl = { status: 'success', summary, details };

      overview.ssl = { status, text };
    }
  }

  let impersonation;
  if (impersonationDoc.exists) {
    const data = impersonationDoc.data();
    if (!createdAt) createdAt = data.createdAt;

    if (data.status && data.status === 'error') {
      overview.impersonation = { status: 'error' };
      impersonation = { status: 'error' };
    } else {
      const summary = {
        rank: data.result.rank,
        createdAt: data.createdAt,
        count: data.result.czds.count,
        bimi: data.result.bimi,
        brandTld: data.result.brand_tld,
        vmc: !!data.result.vmc && !!data.result.vmc.subject,
        spf: data.result?.spf !== undefined ? data.result.spf : null,
        dmarc: data.result?.dmarc !== undefined ? (data.result.dmarc !== null) : null,
      };
      const details = data.result.czds.results.map(r => ({ fqdn: r.domain_nm })).slice(0, 10);
      impersonation = { status: 'success', summary, details };

      overview.impersonation = { status: 'rank', rank: data.result.rank };
    }
  }

  overview.createdAt = convertUTCtoJST(createdAt || new Date((new Date(expiredAt)).getTime() - TTL_MILLISECOND));
  return reply.send({ status: 'success', message: null, result: { configuration, overview, nds, cloud, ssl, impersonation } });
};

export default { schema, handler };
