import { Readable } from 'stream';
import axios from 'axios';

const USER_FLOW_CHAT = 'chat';
const USER_FLOW_DIAGNOSIS = 'diagnosis';

export const sendMessage = async (post, messages, path, clientIp) => {
  if (process.env.INTERNAL_API_AI_MAINTENANCE && process.env.INTERNAL_API_AI_MAINTENANCE === 'true') {
    return new Readable({
      read(size) {
        this.push('申し訳ございません。ただいまメンテナンス中です。時間をおいてお試しください。');
        this.push(null);
      },
    });
  }
  try {
    const user_flow = getUserFlow(path);
    const params = { post, messages, user_flow, client_ip: clientIp };

    const { data, status } = await axios.post(`${process.env.INTERNAL_API_HOST}/internal-api-ai`, params, { responseType: 'stream' });

    if (status < 200 || status >= 300) {
      throw new Error(`InternalAPiAi request error response: ${status}`);
    }
    return data;
  } catch (err) {
    console.error(new Error(`Internal Api AI Error post: ${post}, message: ${messages}`));
    throw err;
  }
};

const getUserFlow = (path) => {
  if (path.includes('check/password')) {
    return USER_FLOW_DIAGNOSIS;
  }
  if (path.includes('check/site-risk')) {
    return USER_FLOW_DIAGNOSIS;
  }
  return USER_FLOW_CHAT;
};
