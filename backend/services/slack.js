import { WebClient } from '@slack/web-api';

export const sendMessage = async (token, channelId, text, threadTs = null, replyBroadcast = false) => {
  if (
    process.env.SLACK_MAINTENANCE
    && process.env.SLACK_MAINTENANCE === 'true'
  ) {
    console.log(
      `Slack is maintenanced channel: ${channelId}, text: ${text}, threadTs: ${threadTs}`,
    );
    return { ts: threadTs ?? 'maintenanceTs' };
  }

  try {
    const messagePayload = { channel: channelId, text, reply_broadcast: replyBroadcast };

    if (threadTs) {
      messagePayload.thread_ts = threadTs;
    }

    const slackClient = new WebClient(token);

    const response = await slackClient.chat.postMessage(messagePayload);
    return response;
  } catch (err) {
    console.error(err);
    return { ts: threadTs ?? 'errorTs' }; // slackエラーがサービスに影響を与えないため
  }
};

export const replaceMessage = async (token, channelId, ts, patterns, replacements) => {
  if (
    process.env.SLACK_MAINTENANCE
    && process.env.SLACK_MAINTENANCE === 'true'
  ) {
    console.log(
      `Slack is maintenanced channel: ${channelId}, ts: ${ts}, patterns: ${patterns}, replacements: ${replacements}`,
    );
    return;
  }

  try {
    const slackClient = new WebClient(token);

    const { messages } = await slackClient.conversations.history({
      channel: channelId,
      latest: ts,
      limit: 1,
      inclusive: true,
    });

    if (messages.length === 0) {
      console.error(`Not found message ts: ${ts}`);
      return;
    }

    const originalMessage = messages[0].text;
    let updatedMessage = originalMessage;
    patterns.forEach((pattern, idx) => {
      if (!replacements[idx]) return;
      const replacement = replacements[idx];
      updatedMessage = updatedMessage.replace(new RegExp(pattern, 'g'), replacement);
    });

    if (originalMessage === updatedMessage) {
      console.error(`Unnecessary replace message  ts: ${ts}, patterns: ${patterns}, replacements: ${replacements}`);
      return;
    }

    await slackClient.chat.update({
      channel: channelId,
      ts: ts,
      text: updatedMessage,
    });
  } catch (err) {
    console.error(err);
    return;
  }
};

export const addReaction = async ({ token, channelId, reaction, ts }) => {
  if (
    process.env.SLACK_MAINTENANCE
    && process.env.SLACK_MAINTENANCE === 'true'
  ) {
    console.log(
      `Slack is maintenanced channel: ${channelId}, reaction: ${reaction}, ts: ${ts}`,
    );
    return;
  }

  try {
    const slackClient = new WebClient(token);
    await slackClient.reactions.add({
      name: reaction,
      channel: channelId,
      timestamp: ts,
    });
  } catch (err) {
    console.error(err);
    return;
  }
};

export const addOrRemoveReaction = async ({
  token,
  channelId,
  targetMessage,
  reaction,
  toggle,
}) => {
  if (
    process.env.SLACK_MAINTENANCE
    && process.env.SLACK_MAINTENANCE === 'true'
  ) {
    return;
  }
  try {
    const slackClient = new WebClient(token);
    await _addOrRemoveReactionInter({
      client: slackClient,
      channelId,
      targetMessage,
      reaction,
      toggle,
    });
  } catch (err) {
    console.error(err);
  }
};

export const getTargetMessage = async ({
  token,
  channelId,
  threadTs,
  replyNumber,
}) => {
  try {
    const slackClient = new WebClient(token);

    const conversation = await slackClient.conversations.replies({
      ts: threadTs,
      channel: channelId,
      limit: 1000,
    });

    if (replyNumber == null) {
      return conversation.messages[0];
    }

    const aiMessages = conversation.messages.filter(
      msg => msg.user == process.env.SLACK_AI_UID,
    );

    let messagePositon = replyNumber;

    // Local環境設定
    if (
      process.env.SECRET_SLACK_USER_TOKEN == process.env.SECRET_SLACK_AI_TOKEN
    ) {
      messagePositon = replyNumber * 2 + 1;
    }

    const targetMessage = aiMessages[messagePositon];
    return targetMessage;
  } catch (err) {
    console.error(err);
    return null;
  }
};

const _addOrRemoveReactionInter = async ({
  client,
  channelId,
  targetMessage,
  reaction,
  toggle,
}) => {
  const currentReactions = targetMessage.reactions || [];

  const existingUserReaction = currentReactions.find(r =>
    r.users.includes(process.env.SLACK_USER_UID),
  );

  if (!toggle && existingUserReaction) {
    return;
  }

  if (existingUserReaction) {
    await client.reactions.remove({
      name: existingUserReaction.name,
      channel: channelId,
      timestamp: targetMessage.ts,
    });
  }

  if (reaction) {
    await client.reactions.add({
      name: reaction,
      channel: channelId,
      timestamp: targetMessage.ts,
    });
  }
};

export const getLastSlackMessage = async (token, channelId) => {
  try {
    const slackClient = new WebClient(token);

    const conversations = await slackClient.conversations.history({
      channel: channelId,
      limit: 1,
    });

    const messages = conversations.messages;

    const lastMessage = messages[0];
    return lastMessage || null;
  } catch (err) {
    console.error(err);
    return null;
  }
};
