import axios from 'axios';

const API_URL = 'https://haveibeenpwned.com/api/v3/breachedaccount';

const BREACH_API_URL = 'https://haveibeenpwned.com/api/v3/breaches';

export const request = async (email) => {
  if (process.env.HIBP_MAINTENANCE && process.env.HIBP_MAINTENANCE === 'true') {
    console.log(`Hibp is maintenanced email: ${email}`);
    return false;
  }

  try {
    const headers = { 'hibp-api-key': process.env.SECRET_HIBP_API_KEY, 'user-agent': 'net-security-marketing' };
    const { status, data } = await axios.get(`${API_URL}/${encodeURIComponent(email)}?truncateResponse=false`, { headers });
    if (status !== 200) {
      throw new Error(`Hibp request error email: ${email}, status: ${status}, data: ${data}`);
    }
    if (!data) {
      throw new Error(`Hibp request error email: ${email}, status: ${status}`);
    }
    return data;
  } catch (err) {
    if (err.response?.status === 404) return []; // 404 is no leaked email

    console.error(new Error(`Hibp Api Error email: ${email}`));
    if (err.response?.status === 400) {
      return false; // 400 is format error
    }
    console.error(JSON.stringify(err));
    throw err;
  }
};

export const getBreaches = async () => {
  const response = await axios.get(BREACH_API_URL);
  return response.data;
};
