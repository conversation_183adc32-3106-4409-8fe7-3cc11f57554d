import axios from 'axios';

export const request = async (fqdn) => {
  if (process.env.NDS_MAINTENANCE && process.env.NDS_MAINTENANCE === 'true') {
    console.log(`Nds is maintenanced fqdn: ${fqdn}`);
    return false;
  }

  try {
    const headers = { 'X-API-KEY': process.env.SECRET_NDS_API_KEY, 'content-type': 'application/json' };
    const params = { fqdn, scan_mode: 'passive', scan_risk_level: 'info' };
    if (process.env.API_HOST !== 'http://localhost:8080') {
      params.webhook_url = `${process.env.API_HOST}/webhook/nds`;
    }

    const { status, data } = await axios.post(`${process.env.NDS_BASE_URL}/v2/scans`, params, { headers });
    console.log(status);
    console.log(data);

    if (status !== 202) {
      throw new Error(`Nds request error fqdn: ${fqdn}, status: ${status}, data: ${data}`);
    }
    if (!data) {
      throw new Error(`Nds request error fqdn: ${fqdn}, status: ${status}`);
    }

    return data;
  } catch (err) {
    console.error(new Error(`Nds Api Error fqdn: ${fqdn}`));
    console.log(JSON.stringify(err));
    throw err;
  }
};
