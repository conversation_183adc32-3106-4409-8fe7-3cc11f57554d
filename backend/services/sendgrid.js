import { EventWebhook } from '@sendgrid/eventwebhook';
import sgMail from '@sendgrid/mail';

sgMail.setApiKey(process.env.SECRET_SENDGRID_API_KEY);

function convertNewlinesToHtmlBreaks(content) {
  return content.replace(/\n/g, '<br>');
}

export const request = async ({ personalizations, content, customArgs }) => {
  if (process.env.SENDGRID_MAINTENANCE && process.env.SENDGRID_MAINTENANCE === 'true') {
    console.log(`Sendgrid is maintenanced: ${JSON.stringify(personalizations)}, content: ${content}`);
    return true;
  }

  const msg = {
    personalizations,
    from: { email: process.env.SENDGRID_FROM_EMAIL, name: process.env.SENDGRID_FROM_NAME },
    customArgs,
    content: [
      {
        type: 'text/plain',
        value: content,
      },
      {
        type: 'text/html',
        value: convertNewlinesToHtmlBreaks(content),
      },
    ],
  };

  try {
    await sgMail.send(msg);
    return true;
  } catch (err) {
    console.error(new Error(`Sendgrid Api Error email: ${JSON.stringify(personalizations)}, content: ${content}`));
    throw err;
  }
};

export const isVerified = ({ key, body, signature, timestamp }) => {
  const webhook = new EventWebhook();
  const publicKey = webhook.convertPublicKeyToECDSA(key);
  return webhook.verifySignature(publicKey, body, signature, timestamp);
};
