import * as google from '@googleapis/sheets';
import { GoogleAuth } from 'google-auth-library';

const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];

export const request = async ({ docId, sheetId, records }) => {
  if (process.env.SPREADSHEET_MAINTENANCE && process.env.SPREADSHEET_MAINTENANCE === 'true') {
    console.log(`Spreadsheet is maintenanced: ${docId}`);
    return false;
  }

  try {
    const auth = new GoogleAuth({ scopes: SCOPES });

    const sheets = google.sheets({ version: 'v4' });
    const sheetMetadata = await sheets.spreadsheets.get({
      auth,
      spreadsheetId: docId,
    });

    const sheet = sheetMetadata.data.sheets?.find(s => s.properties?.sheetId == sheetId);

    if (!sheet || !sheet.properties?.title) {
      throw new Error(`Sheet with ID ${sheetId} not found`);
    }

    const sheetName = sheet.properties.title;

    const response = await sheets.spreadsheets.values.append({
      auth,
      spreadsheetId: docId,
      range: `${sheetName}!A:A`,
      valueInputOption: 'RAW',
      insertDataOption: 'INSERT_ROWS',
      requestBody: { values: records },
    });

    console.log(`Appended ${response.data.updates?.updatedCells} cells to ${sheetName} in ${docId}`);
  } catch (err) {
    console.error(new Error(`SpreadSheet Api Error docId: ${docId} sheetId: ${sheetId}`));
    console.log(JSON.stringify(err));
    throw err;
  }
};
