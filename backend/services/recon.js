import axios from 'axios';

export const request = async (fqdn) => {
  if (process.env.RECON_MAINTENANCE && process.env.RECON_MAINTENANCE === 'true') {
    console.log(`Recon is maintenanced fqdn: ${fqdn}`);
    return false;
  }

  try {
    const { status, data } = await axios.get(`${process.env.RECON_BASE_URL}/gmo-net-security-marketing/check?fqdn=${fqdn}`);
    // console.log(status);
    console.log(data);

    if (status !== 200) {
      throw new Error(`Recon request error fqdn: ${fqdn}, status: ${status}, data: ${data}`);
    }
    if (!data) {
      throw new Error(`Recon request error fqdn: ${fqdn}, status: ${status}`);
    }

    return { status: 'success', data };
  } catch (err) {
    if (err.response?.data?.message === 'failed to lookup IP') return { status: 'error', data: [] };
    console.error(new Error(`Recon Api Error fqdn: ${fqdn}`));
    console.log(JSON.stringify(err));
    throw err;
  }
};
