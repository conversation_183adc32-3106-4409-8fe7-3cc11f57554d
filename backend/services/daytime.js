export const convertUTCtoJST = (utcString) => {
  try {
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid date');
    }

    const jstDate = utcDate.toLocaleString('en-US', {
      timeZone: 'JST',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    const [date, time] = jstDate.split(', ');
    const [month, day, year] = date.split('/');
    const formattedDate = `${year}-${month}-${day} ${time}`;

    return formattedDate;
  } catch (error) {
    return '????-??-?? ??:??:??';
  }
};

export const convertDate = (dateString) => {
  const regex = /^(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2}) ([+-]\d{4}) UTC$/;
  const match = dateString.match(regex);

  if (match) {
    const [_, datePart, timePart, timezonePart] = match;
    const formattedDate = `${datePart}T${timePart}${timezonePart}`;
    return new Date(formattedDate);
  } else {
    console.error('Invalid date format');
    return new Date();
  }
};
