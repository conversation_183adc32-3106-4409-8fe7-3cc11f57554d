import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_HIBP } from '../constants/collections.js';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { TEMPLATE_REGULARLY_ANNOUNCE } from '../constants/template.js';
import { TOPIC_CHECK_EMAIL, TOPIC_SEND_EMAIL } from '../constants/topics.js';
import { publishCheckFqdn } from '../handlers/post_fqdn.js';
import firestore from '../providers/firestore.js';
import { encrypt } from '../services/cryptography.js';

const INPUT = [
  { email: '<EMAIL>', fqdn: 'gmo.jp' },
  { email: '<EMAIL>', fqdn: 'hogehoge.gmo.jp' },
];

const run = async () => {
  for (const i of INPUT) {
    await publishCheckFqdn(i.email, i.fqdn, false);
  }
};

run();
