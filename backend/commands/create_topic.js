import { PubSub } from '@google-cloud/pubsub';
import { TOPIC_SEND_EMAIL, TOPIC_CHECK_EMAIL, TOPIC_CHECK_FQDN, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_PASSWORD_HISTORY, TOPIC_WRITE_SITE_RISK_HISTORY, TOPIC_WRITE_SPREADSHEET } from '../constants/topics.js';

const pubSubClient = new PubSub();

let topic, res, sub;
[topic, res] = await pubSubClient.createTopic(TOPIC_SEND_EMAIL);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'emailRequestSubscription', { pushEndpoint: `http://localhost:18080/projects/test-security/topics/${TOPIC_SEND_EMAIL}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_CHECK_EMAIL);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'hibpRequestSubscription', { pushEndpoint: `http://localhost:18081/projects/test-security/topics/${TOPIC_CHECK_EMAIL}` });
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'checkEmailReservationSubscription', { pushEndpoint: `http://localhost:18086/projects/test-security/topics/${TOPIC_CHECK_EMAIL}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_CHECK_FQDN);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'impersonationRequestSubscription', { pushEndpoint: `http://localhost:18082/projects/test-security/topics/${TOPIC_CHECK_FQDN}` });
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'ndsRequestSubscription', { pushEndpoint: `http://localhost:18083/projects/test-security/topics/${TOPIC_CHECK_FQDN}` });
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'reconRequestSubscription', { pushEndpoint: `http://localhost:18084/projects/test-security/topics/${TOPIC_CHECK_FQDN}` });
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'checkFqdnReservationSubscription', { pushEndpoint: `http://localhost:18087/projects/test-security/topics/${TOPIC_CHECK_FQDN}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_SEND_SLACK_MESSAGE);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'sendSlackMessageRequestSubscription', { pushEndpoint: `http://localhost:18085/projects/test-security/topics/${TOPIC_SEND_SLACK_MESSAGE}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_WRITE_PASSWORD_HISTORY);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'passwordHistoryRequestSubscription', { pushEndpoint: `http://localhost:18088/projects/test-security/topics/${TOPIC_WRITE_PASSWORD_HISTORY}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_WRITE_SITE_RISK_HISTORY);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'siteRiskHistoryRequestSubscription', { pushEndpoint: `http://localhost:18089/projects/test-security/topics/${TOPIC_WRITE_SITE_RISK_HISTORY}` });
console.log(res);

[topic, res] = await pubSubClient.createTopic(TOPIC_WRITE_SPREADSHEET);
console.log(res);
[sub, res] = await pubSubClient.createSubscription(topic, 'spreadsheetRequestSubscription', { pushEndpoint: `http://localhost:18090/projects/test-security/topics/${TOPIC_WRITE_SPREADSHEET}` });
