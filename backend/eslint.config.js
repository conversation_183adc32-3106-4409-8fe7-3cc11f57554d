import stylistic from '@stylistic/eslint-plugin';
import importPlugin from 'eslint-plugin-import';

export default [
  stylistic.configs.recommended,
  {
    plugins: {
      '@stylistic': stylistic,
      'import': importPlugin,
    },
    rules: {
      '@stylistic/brace-style': ['error', '1tbs', { allowSingleLine: true }],
      '@stylistic/comma-dangle': ['error', 'always-multiline'],
      '@stylistic/indent': ['error', 2],
      '@stylistic/max-len': ['error', { code: 160 }],
      '@stylistic/object-curly-spacing': ['error', 'always'],
      '@stylistic/object-curly-newline': ['error', { multiline: true }],
      '@stylistic/quotes': [
        'error',
        'single',
        { avoidEscape: true, allowTemplateLiterals: true },
      ],
      '@stylistic/semi': ['error', 'always'],
      'no-console': [
        'error',
        { allow: ['warn', 'error'] },
      ],
      'import/order': [
        'error',
        { alphabetize: { order: 'asc' } },
      ],
    },
  },
];
