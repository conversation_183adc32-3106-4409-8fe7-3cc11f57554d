import { Storage } from '@google-cloud/storage';

const storage = new Storage({ projectId: process.env.PROJECT_ID });

const resourceCacheControl = [
  {
    prefix: 'static/css/',
    cacheControl: 'public, max-age=60, s-maxage=120',
  },
  {
    prefix: 'static/fonts/',
    cacheControl: 'public, max-age=300, s-maxage=600',
  },
  {
    prefix: 'static/img/',
    cacheControl: 'public, max-age=300, s-maxage=600',
  },
  {
    prefix: 'static/js/',
    cacheControl: 'public, max-age=60, s-maxage=120',
  },
  {
    prefix: 'static/json/',
    cacheControl: 'public, max-age=1800, s-maxage=3600',
  },
];

const subscriber = async ({ data }) => {
  const file = data;
  console.log(file);
  if (!file || !file.name || !resourceCacheControl.some(({ prefix }) => file.name.startsWith(prefix))) {
    console.log(`${file.name} is not static file`);
    return;
  }

  for (const { prefix, cacheControl } of resourceCacheControl) {
    const [files] = await storage.bucket(process.env.STATIC_FILE_BUCKET_NAME).getFiles({ prefix });
    for (const file of files) {
      await file.setMetadata({ cacheControl });
    }
  }
};

export default subscriber;
