import { isCompanyEmail } from 'free-email-domains-list';
import { COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { CONSTANT_DNS_TXT_RECORD_KEY } from '../constants/constants.js';
import { isDomainMatch, publishCheckFqdn } from '../handlers/post_fqdn.js';
import { getDocsByNextCheckedAt } from '../providers/firestore.js';
import { hash, priDecrypt } from '../services/cryptography.js';
import { isVerified } from '../services/dns.js';

const api = async (_, res) => {
  try {
    const now = new Date();
    const reservationDocs = await getDocsByNextCheckedAt({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, to: now.toISOString() });
    for (const doc of reservationDocs) {
      const { encryptedEmail, fqdn, isRegularly, interval } = doc.data();
      if (!isRegularly) {
        console.error(new Error(`Configured isRegularly = false id: ${doc.id}`));
        continue;
      }

      const email = await priDecrypt(encryptedEmail, process.env.SECRET_PRIVATE_KEY);
      const authTxt = await hash(`${email}:${fqdn}`);
      if (!(isCompanyEmail(email) && isDomainMatch(email, fqdn)) && !(await isVerified(fqdn, CONSTANT_DNS_TXT_RECORD_KEY, authTxt))) {
        console.error(new Error(`Unverify fqdn id: ${doc.id}`));
        await doc.ref.update({ isRegularly: false, nextCheckedAt: null });
        continue;
      }

      const nextCheckedAt = new Date();
      nextCheckedAt.setMonth(nextCheckedAt.getMonth() + interval);
      doc.ref.update({ nextCheckedAt: nextCheckedAt.toISOString() });

      await publishCheckFqdn(email, fqdn, true);
    }

    res.status(200).send('OK');
  } catch (err) {
    console.error(err);
    res.status(500).send('ERROR');
  }
};

export default api;
