import { COLLECTION_NDS } from '../constants/collections.js';
import { docExists, saveDoc } from '../providers/firestore.js';
import { request } from '../services/nds.js';

const subscriber = async ({ data }) => {
  const { email, fqdn, createdAt, expiredAt, code, threadTs, isRegularly = false } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !fqdn || !createdAt || !expiredAt || !code || !threadTs) {
    console.error(new Error('email, fqdn, createdAt, expiredAt code and threadTs are required'));
    return;
  }

  if (await docExists({ collection: COLLECTION_NDS, docId: code })) {
    console.error(new Error(`nds record dupricated code: ${code}`));
    return;
  }

  const result = await request(fqdn);
  if (!result) return;

  const scanId = result.scan_id + '';
  const dataNDS = { scanId, email, fqdn, expiredAt, threadTs: threadTs, isRegularly };
  await saveDoc({ collection: COLLECTION_NDS, docId: code, data: dataNDS });
};

export default subscriber;
