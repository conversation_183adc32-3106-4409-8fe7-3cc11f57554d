import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { publishCheckEmail } from '../handlers/post_email.js';
import { getDocsByNextCheckedAt } from '../providers/firestore.js';
import { priDecrypt } from '../services/cryptography.js';

const api = async (_, res) => {
  try {
    const now = new Date();
    const reservationDocs = await getDocsByNextCheckedAt({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, to: now.toISOString() });

    for (const doc of reservationDocs) {
      const { encryptedEmail, isRegularly, interval } = doc.data();
      if (!isRegularly) {
        console.error(new Error(`Configured isRegularly = false id: ${doc.id}`));
        continue;
      }

      const email = await priDecrypt(encryptedEmail, process.env.SECRET_PRIVATE_KEY);
      const nextCheckedAt = new Date();
      nextCheckedAt.setMonth(nextCheckedAt.getMonth() + interval);
      doc.ref.update({ nextCheckedAt: nextCheckedAt.toISOString() });

      await publishCheckEmail(email, true);
    }

    res.send('OK');
  } catch (err) {
    console.error(err);
    res.status(500).send('ERROR');
  }
};

export default api;
