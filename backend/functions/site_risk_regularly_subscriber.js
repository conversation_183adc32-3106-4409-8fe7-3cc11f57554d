import { COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { getDocsByDateRange } from '../providers/firestore.js';
import { priDecrypt } from '../services/cryptography.js';

// priDecrypt テスト用
const subscriber = async ({ _ }) => {
  try {
    const docs = await getDocsByDateRange({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, from: '2025-02-27T00:00:00', to: '2025-03-01T00:00:00' });

    const emails = await Promise.all(docs.map(({ encryptedEmail }) => priDecrypt(encryptedEmail)));
    console.log(emails.join(', '));
  } catch (err) {
    console.error(err);
  }
};

export default subscriber;
