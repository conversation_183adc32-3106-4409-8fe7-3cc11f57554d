import { generateWatchHibpMessage } from '../constants/slack_template.js';
import { getBreaches } from '../services/hibp.js';
import { sendMessage, getLastSlackMessage } from '../services/slack.js';

const DEFAULT_DATE = '2025-02-06T00:00:00Z';

const api = async (_, res) => {
  try {
    const lastMessage = await getLastSlackMessage(process.env.SECRET_SLACK_AI_TOKEN, process.env.SLACK_CHAT_CHANNEL_ID);
    const lastPostTime = lastMessage ? new Date(lastMessage.ts * 1000) : new Date(DEFAULT_DATE);

    const breaches = await getBreaches();
    const recentBreaches = getRecentPasswordBreaches(breaches, lastPostTime);

    const message = generateWatchHibpMessage(recentBreaches);
    await sendMessage(process.env.SECRET_SLACK_AI_TOKEN, process.env.SLACK_CHAT_CHANNEL_ID, message);

    res.send('OK');
  } catch (error) {
    console.error(error);
    res.status(500).send('ERROR');
  }
};

const getRecentPasswordBreaches = (breaches, minModifiedDate) => {
  return breaches.filter((breach) => {
    const breachModifiedTime = new Date(breach.ModifiedDate);
    return breachModifiedTime.getTime() > minModifiedDate.getTime() && breach.DataClasses.includes('Passwords');
  });
};

export default api;
