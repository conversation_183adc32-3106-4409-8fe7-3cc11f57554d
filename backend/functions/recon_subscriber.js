import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_SSL, COLLECTION_CLOUD } from '../constants/collections.js';
import { SLACK_REPLACEMENT_TAMPLATE_CLOUD, SLACK_REPLACEMENT_TAMPLATE_SSL, SLACK_TEMPLATE_CLOUD_RESULT, SLACK_TEMPLATE_SSL_RESULT } from '../constants/slack_template.js';
import { TEMPLATE_RECON_READY, TEMPLATE_REGULARLY_RECON_READY } from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_SITE_RISK_HISTORY } from '../constants/topics.js';
import { docExists, saveDoc } from '../providers/firestore.js';
import { request } from '../services/recon.js';
import { createCloudSink, createSslSink } from '../services/sink.js';

const pubSubClient = new PubSub();

const subscriber = async ({ data }) => {
  const { email, fqdn, createdAt, expiredAt, code, threadTs, isRegularly = false } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !fqdn || !createdAt || !expiredAt || !code || !threadTs) {
    console.error(new Error('email, fqdn, createdAt, expiredAt, code and threadTs are required'));
    return;
  }

  if (await docExists({ collection: COLLECTION_CLOUD, docId: code })) {
    console.error(new Error(`cloud record duplicated code: ${code}`));
    return;
  }

  if (await docExists({ collection: COLLECTION_SSL, docId: code })) {
    console.error(new Error(`ssl record duplicated code: ${code}`));
    return;
  }

  const result = await request(fqdn);
  if (!result) return;

  const clouds = result.data.map(({ public_cloud, ip }) => ({ ...public_cloud, ip }));
  const ssls = result.data.map(({ ssl, ip }) => ({ ...ssl, ip }));

  const cloudLog = await createCloudSink(email, fqdn, createdAt, expiredAt, code, { status: result.status, result: clouds }, isRegularly);
  const sslLog = await createSslSink(email, fqdn, createdAt, expiredAt, code, { status: result.status, result: ssls }, isRegularly);

  const dataCloud = { fqdn, createdAt, expiredAt, result: clouds, status: result.status, isRegularly };
  const dataSsl = { fqdn, createdAt, expiredAt, result: ssls, status: result.status, isRegularly };

  await saveDoc({ collection: COLLECTION_CLOUD, docId: code, data: dataCloud });
  await saveDoc({ collection: COLLECTION_SSL, docId: code, data: dataSsl });

  let topic = pubSubClient.topic(TOPIC_WRITE_SITE_RISK_HISTORY);

  let message = Buffer.from(JSON.stringify({ email, fqdn, result: { cloud: cloudLog } }));
  await topic.publishMessage({ data: message });

  message = Buffer.from(JSON.stringify({ email, fqdn, result: { ssl: sslLog } }));
  await topic.publishMessage({ data: message });

  topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);

  message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, message: { template: SLACK_TEMPLATE_CLOUD_RESULT, params: cloudLog }, threadTs }));
  await topic.publishMessage({ data: message });

  message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, message: { template: SLACK_TEMPLATE_SSL_RESULT, params: sslLog }, threadTs }));
  await topic.publishMessage({ data: message });

  message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, threadTs, updateOptions: [{ template: SLACK_REPLACEMENT_TAMPLATE_CLOUD, params: cloudLog }, { template: SLACK_REPLACEMENT_TAMPLATE_SSL, params: sslLog }] }));
  await topic.publishMessage({ data: message });

  // nds がメンテの時、メール送信する
  if (process.env.NDS_MAINTENANCE && process.env.NDS_MAINTENANCE === 'true') {
    topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
    const url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${code}`;
    message = Buffer.from(JSON.stringify({ email, template: isRegularly ? TEMPLATE_REGULARLY_RECON_READY : TEMPLATE_RECON_READY, params: { createdAt, expiredAt, fqdn, url } }));
    await topic.publishMessage({ data: message });
  }
};

export default subscriber;
