import fs from 'fs';
import path from 'path';
import { Storage } from '@google-cloud/storage';
import { v4 as uuidv4 } from 'uuid';

const storage = new Storage({ projectId: process.env.PROJECT_ID });

const bucketName = process.env.PROMPT_BUCKET_NAME;
const inputPrefix = 'input/';
const outputFileName = 'bundle';

const prefixOfFirstStep = 'input/0';
const prefixOfSecondStep = 'input/1';
const prefixOfThirdStep = 'input/2';

const getFilesByPrefix = (files, prefix) => {
  return files
    .filter(file => file.name.startsWith(prefix))
    .sort((a, b) => {
      if (a.name < b.name) return -1;
      if (a.name > b.name) return 1;
      return 0;
    });
};

const downloadAndWriteFiles = async (files, writeStream) => {
  for (const file of files) {
    const [contents] = await file.download();
    writeStream.write(contents);
    writeStream.write('\n');
  }
};

const subcriber = async ({ data }) => {
  const file = data;
  console.log(file);
  if (!file || !file.name || !file.name.startsWith(inputPrefix)) {
    console.log('file.name is not in /input');
    return;
  }
  const tempFilePath = path.join('/tmp', uuidv4());

  try {
    const writeStream = fs.createWriteStream(tempFilePath);
    const [files] = await storage.bucket(bucketName).getFiles({ prefix: inputPrefix });

    const firstFiles = getFilesByPrefix(files, prefixOfFirstStep);
    await downloadAndWriteFiles(firstFiles, writeStream);

    writeStream.write('\n<context_GMOネットセキュリティのサービス>\n');

    const secondFiles = getFilesByPrefix(files, prefixOfSecondStep);
    await downloadAndWriteFiles(secondFiles, writeStream);

    writeStream.write('</context_GMOネットセキュリティのサービス>\n\n');

    const thirdFiles = getFilesByPrefix(files, prefixOfThirdStep);
    await downloadAndWriteFiles(thirdFiles, writeStream);
    writeStream.end();

    await storage.bucket(bucketName).upload(tempFilePath, { destination: outputFileName });

    console.log(`Bundle file created at ${outputFileName}`);
  } catch (err) {
    throw err;
  } finally {
    fs.unlinkSync(tempFilePath);
  }
};

export default subcriber;
