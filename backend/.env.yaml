ENV: local
PROJECT_ID: test-security
HOST: http://localhost:3000
API_HOST: http://localhost:8080
PATH_PREFIX: /security

DNS_DRY_RUN: "true"
HIBP_MAINTENANCE: "false"
IMPERSONATION_MAINTENANCE: "false"
INTERNAL_API_AI_MAINTENANCE: "false"
NDS_MAINTENANCE: "true"
RECAPTCHA_DRY_RUN: "true"
RECON_MAINTENANCE: "false"
SENDGRID_MAINTENANCE: "true"
SLACK_MAINTENANCE: "true"
SPREADSHEET_MAINTENANCE: "true"
TLD_MAINTENANCE: "false"

INTERNAL_API_HOST: http://localhost:8081
NDS_BASE_URL: https://api.stg.shindan.gmo-cybersecurity.com
RECON_BASE_URL: https://nds-recon-api-************.asia-northeast1.run.app
SENDGRID_FROM_EMAIL: <EMAIL>
SENDGRID_FROM_NAME: GMOセキュリティ24
SLACK_CHAT_CHANNEL_ID: C083BQGSZJ4
SLACK_FQDN_CHANNEL_ID: C083BQGSZJ4
SLACK_PASSWORD_CONTACT_CHANNEL_ID: C083BQGSZJ4
SLACK_BRAND_TLD_CONTACT_CHANNEL_ID: C083BQGSZJ4
SLACK_AI_UID: U088J2FRZGA
SLACK_USER_UID: U088J2FRZGA
STATIC_FILE_BUCKET_NAME: dev-security-static
PROMPT_BUCKET_NAME: dev-security-ai
CONTACT_SPREADSHEET_DOC_ID: 13kkUDmcRlGZX-p3UAg9RbdAG4DLZO1E1TKReYN7yg8k
NDS_SPREADSHEET_SHEET_ID: "0"
CLOUD_SPREADSHEET_SHEET_ID: "1340492226"
SSL_SPREADSHEET_SHEET_ID: "936608786"
IMPERSONATION_SPREADSHEET_SHEET_ID: "34533792"
BRAND_TLD_SPREADSHEET_SHEET_ID: "112526226"

PREVIEW_PASSWORD_CONFIGURATION_ID: <EMAIL>
PREVIEW_SITE_RISK_CONFIGURATION_ID: 65745ea06858e5fe2b0d366b7d87ebee39f080d4
# 開発環境用
PUBSUB_EMULATOR_HOST: localhost:8043
FIRESTORE_EMULATOR_HOST: localhost:8045

# 本番はSecretManagerで管理をする
SECRET_CRYPTOGRAPHY_PASSWORD: Vnl1(zFAc)Wo3}nu
SECRET_CRYPTOGRAPHY_SALT: wiG?dsNWcythI{DV
SECRET_HIBP_API_KEY: 13e9474514e14c5d8ea114a0f1184847
SECRET_LEAK_CHECK_API_KEY: 6ce68c3dc90d347fde01a664d7b4cb90da9c29c3
SECRET_NDS_API_KEY: gmo_internetgroup_04_10ce016aa038b9cf7c79
SECRET_PUBLIC_KEY: "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEA2OFsVOC3nuEbl1QZAak/3z9oJkHhNhVUsWbxcGRXfurjiSPOxUBF2A0yDka7W0M21Bc+Xf3oSLCzrMfFHo1hba8u8zGVAkt21MwaCIJqJGrFV3FpZLIZ\njHmDrzmTIEDreey/UAzl2hVXKkIGIk8UKTAew8ycLVl8q/fVYUzhxhUD8rjEkORo\nfd8S3uOVOxUswvsfku+6wwCHGNWKlwtn84t1yq0fRn0U5FHEkbkiFxQZPxOS3gQi\nQbPiGtXaqUR2vWnPvw2JqXSBjCoJQrv+Lf/u0haRWLEE6dM/ve2l1WraP2MwvguP\nDH+nTEqsBi6qVabn4m7xyiwb6805rgN0vQIDAQAB\n-----END RSA PUBLIC KEY-----"
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
SECRET_RECAPTCHA_SECRET: 6LdzCaYqAAAAAI7iL4RmYxKEZobT_yEcnXXcnB_-
SECRET_SENDGRID_API_KEY: SG.dummy_key
SECRET_SENDGRID_PUBLIC_KEY: dummy_key
SECRET_SLACK_USER_TOKEN: *********************************************************
SECRET_SLACK_AI_TOKEN: *********************************************************
SECRET_EXCHANGE_RATES_API_KEY: YOUR_API_KEY
