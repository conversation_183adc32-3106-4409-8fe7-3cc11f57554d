export const TEMPLATE_CONFIRM_EMAIL = 'CONFIRM_EMAIL';
export const TEMPLATE_HIBP_READY = 'HIBP_READY_TEMPLATE';
export const TEMPLATE_IMPERSONATION_READY = 'IMPERSONATION_READY_TEMPLATE';
export const TEMPLATE_NDS_READY = 'NDS_READY_TEMPLATE';
export const TEMPLATE_RECON_READY = 'RECON_READY_TEMPLATE';
export const TEMPLATE_PASSWORD_CONTACT_THANKS = 'PASSWORD_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_SITE_RISK_CONTACT_THANKS = 'SITE_RISK_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_NDS_CONTACT_THANKS = 'NDS_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_CLOUD_CONTACT_THANKS = 'CLOUD_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_SSL_CONTACT_THANKS = 'TEMPLATE_SSL_CONTACT_THANKS';
export const TEMPLATE_IMPERSONATION_CONTACT_THANKS = 'IMPERSONATION_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_BRAND_TLD_CONTACT_THANKS = 'BRAND_TLD_CONTACT_THANKS_TEMPLATE';

export const TEMPLATE_REGULARLY_ANNOUNCE = 'REGULARLY_ANNOUNCE';

export const TEMPLATE_REGULARLY_HIBP_READY = 'REGULARLY_HIBP_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_IMPERSONATION_READY = 'REGULARLY_IMPERSONATION_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_NDS_READY = 'REGULARLY_NDS_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_RECON_READY = 'REGULARLY_RECON_READY_TEMPLATE';

const footer = () => `
――――――――――――――――――――――――
GMO インターネットグループ株式会社（東証プライム 証券コード 9449）
URL https://www.gmo.jp/
――――――――――――――――――――――――
おかげさまで 29 周年 すべての人にインターネット
■GMO INTERNET GROUP■ https://www.gmo.jp/
-----------------------------------------------------------------
総合ネットセキュリティ・サービス
「GMO セキュリティ 24」
①パスワード漏洩診断
②Web リスク診断
③セキュリティ相談 AI
24 時間・すべて無料 https://security24.gmo
〜すべての人に安心な未来を〜
――――――――――――――――――――――――
機密情報に関する注意事項：この E-mail は、発信者が意図した
受信者による閲覧・利用を目的としたものです。万一、貴殿が
意図された受信者でない場合には、直ちに送信者に連絡のうえ、
この E-mail を破棄願います。
`;

export const generateConfirmEmailContent = (expiredAt, fqdn, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

本メールはお客様が実施されました「サイトリスク診断」のセキュリティ確保のためのご本人確認となります。
※このメールに心当たりがない場合は、破棄していただきますようお願いいたします。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
こちらのリンクをクリックすることで、本人確認および診断受付が完了となります。
${url}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

今後もお客様にご満足いただけるサービス提供・改善に邁進してまいりますので、変わらぬご愛顧を賜りますようお願い申しあげます。

※本メールアドレスは送信専用となります。
※本メールアドレス宛への返信は受付できませんのでご了承ください。

${footer()}
`;

export const generateHibpContent = (createdAt, expiredAt, email, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

お客様が実施されました「パスワード漏洩診断」の診断結果をお知らせいたします。


ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${email}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※自動で診断を実施する「定期診断機能」は、上記URLから設定変更が可能です。

${footer()}
`;

export const generateNdsContent = (createdAt, expiredAt, fqdn, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

お客様が実施されました「サイトリスク診断」の診断結果をお知らせいたします。

この診断結果は、WEBサイトのリスクを可視化したものです。
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${fqdn}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※自動で診断を実施する「定期診断機能」は、上記URLから設定変更が可能です。

${footer()}
`;

export const generatePasswordContactThanksContent = (consultation, contactedAt) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

このたび、以下の内容でお問い合わせを受け付けました。​

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【お問い合わせ日時】 ${contactedAt}
【お問い合わせ項目】 パスワード漏洩診断
【お問い合わせ内容】 ${consultation}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

担当者より3営業日以内にご連絡いたしますので、今しばらくお待ちください。​
引き続き、「GMOセキュリティ24」をよろしくお願い申し上げます。​

${footer()}
`;

export const generateNdsContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、脆弱性対策の無料見積のお問い合わせをいただき、誠にありがとうございます。

GMOグローバルサインホールディングスのセキュリティ対策担当者より可能な限り早急に、遅くとも1営業日以内にはご連絡させていただきます。

お急ぎのところお待たせし大変恐縮でございますが、心を込めて真摯に対応させていただきますので、今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateCloudContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、クラウドの中身も診断(無料お試し) をお申込みいただきありがとうございます。

お試し用のアカウント発行を可能な限り早急に、遅くとも1営業日以内にはご連絡させていただきます。

お急ぎのところお待たせし大変恐縮ではございますが今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateSslContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、信頼性の高いSSL(無料お試し)のお申込みをいただきありがとうございます。

担当者より1営業日以内にサポートのお電話を差し上げます。

お急ぎのところお待たせし大変恐縮でございますが、心を込めて真摯に対応させていただきますので、今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateImpersonationContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、なりすまし対策の無料見積のお問い合わせをいただき、誠にありがとうございます。

GMOブランドセキュリティのなりすまし対策担当より可能な限り早急に、遅くとも1営業日以内にはご連絡させていただきます。

お急ぎのところお待たせし大変恐縮でございますが、心を込めて真摯に対応させていただきますので、今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateBrandTldContactThanksContent = fullname => `
「GMO『.貴社名』申請・運用支援サービス」をご利用いただき、誠にありがとうございます。
この度、お問い合わせを受け付けいたしました。

担当者より1営業日以内にご連絡いたしますので、今しばらくお待ちください。
引き続きよろしくお願い申し上げます。

${footer()}
`;

export const generateSiteRiskContactThanksContent = (targets, consultation, contactedAt) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

このたび、以下の内容でお問い合わせを受け付けました。​

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【お問い合わせ日時】 ${contactedAt}
【お問い合わせ項目】 ${targets.join('・')}
【お問い合わせ内容】 ${consultation}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

担当者より3営業日以内にご連絡いたしますので、今しばらくお待ちください。​
引き続き、「GMOセキュリティ24」をよろしくお願い申し上げます。​

${footer()}
`;

export const generateAnnounceRegularly = (password, siteRisks) => `
先日はGMOセキュリティ24 (https://www.gmo.jp/security/) をご利用いただきありがとうございました。
この度、無料でご利用いただける「定期診断機能」を新規にリリースいたしましたのでご案内いたします。

「定期診断機能」は先日ご利用いただいたセキュリティ診断を自動で定期的に診断実行する機能です。
セキュリティリスクの早期発見・対策につながる機能ですので、是非ご利用ください。

 ---＼【設定推奨】以下URLからワンクリックで設定可能／--------
${password
  ? `
【パスワード漏洩診断】
【${password.email}】${password.url}`
  : ''}
${siteRisks && siteRisks.length > 0
  ? `
【Webサイトリスク診断】
${siteRisks.map(({ fqdn, url }) => `【${fqdn}】${url}`).join('\n')}`
  : ''}

 -----------------------------------------------------------------------------

■ 定期診断を推奨する理由について
─────────────────

2024年には「1日あたり100件以上※1」の新しい脆弱性が発表されており、
日々何もしていなくても診断結果は悪化し、サイバー攻撃被害のリスクが高まる可能性がございます。

また、最近のサイバー攻撃の被害は大手企業のみにとどまらず、個人や中小企業までに及びます。

このような被害から身を守るために、定期的な診断によるセキュリティリスクの早期発見が必要となります。
「定期診断」は、人間における健康診断のような役割を果たします。

※1：https://www.cvedetails.com/browse-by-date.php

■ 定期診断の頻度について
─────────────────

上記URLより、「1ヶ月毎・3ヶ月毎・6ヶ月毎」の頻度が選択できます。
GMOセキュリティ24では、「月に1回」の定期診断を推奨しております。

※定期診断機能をOFFにすることで停止することが可能です。

本機能がセキュリティ対策に貢献できることを願っております。
今後ともどうぞよろしくお願い申し上げます。

【注意事項】
※一時的に診断レポートの閲覧期限を延長しております。2025/4/1に通常の期限へ戻します。

${footer()}
`;

export const generateRegularlyHibpContent = (createdAt, expiredAt, email, url) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。

「パスワード漏洩診断」の「定期診断」が実施されましたため、診断結果をお知らせいたします。※1
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${email}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※1 定期診断は、定期的に自動で診断を実施する機能で、過去の診断時に設定された頻度で診断が行われます。
　　設定は上記URLから変更可能です。

${footer()}
`;

export const generateRegularlyNdsContent = (createdAt, expiredAt, fqdn, url) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。

「サイトリスク診断」の「定期診断」が実施されましたため、診断結果をお知らせいたします。※1

この診断結果は、WEBサイトのリスクを可視化したものです。
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${fqdn}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※1 定期診断は、定期的に自動で診断を実施する機能で、過去の診断時に設定された頻度で診断が行われます。
　　設定は上記URLから変更可能です。

${footer()}
`;
