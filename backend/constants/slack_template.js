import { PATTERN_CLOUD, PATTERN_IMPERSONATION, PATTERN_NDS, PATTERN_SSL } from './patterns.js';

// export const SLACK_TEMPLATE_CHECK_FQDN = 'SLACK_TEMPLATE_CHECK_FQDN';
export const SLACK_TEMPLATE_CLOUD_RESULT = 'SLACK_TEMPLATE_CLOUD_RESULT';
export const SLACK_TEMPLATE_IMPERSONATION_RESULT = 'SLACK_TEMPLATE_IMPERSONATION_RESULT';
export const SLACK_TEMPLATE_NDS_RESULT = 'SLACK_TEMPLATE_NDS_RESULT';
export const SLACK_TEMPLATE_SSL_RESULT = 'SLACK_TEMPLATE_SSL_RESULT';

export const SLACK_TEMPLATE_PASSWORD_CONTACT = 'SLACK_TEMPLATE_PASSWORD_CONTACT';
export const SLACK_TEMPLATE_SITE_RISK_CONTACT = 'SLACK_TEMPLATE_SITE_RISK_CONTACT';
export const SLACK_TEMPLATE_BRAND_TLD_CONTACT = 'SLACK_TEMPLATE_BRAND_TLD_CONTACT';

export const SLACK_REPLACEMENT_TAMPLATE_CLOUD = 'SLACK_REPLACEMENT_TAMPLATE_CLOUD';
export const SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION = 'SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION';
export const SLACK_REPLACEMENT_TAMPLATE_NDS = 'SLACK_REPLACEMENT_TAMPLATE_NDS';
export const SLACK_REPLACEMENT_TAMPLATE_SSL = 'SLACK_REPLACEMENT_TAMPLATE_SSL';

export const generateCheckFqdnMessage = (fqdn, code, isRegularly = false) => `${fqdn}
- 結果${isRegularly ? '（定期）' : ''}:
    - Webサイト脆弱性診断
        - ${PATTERN_NDS}
    - クラウド利用・リスク診断
        - ${PATTERN_CLOUD}
    - 実在証明・盗聴防止（SSL）診断
        - ${PATTERN_SSL}
    - なりすまし診断
        - ${PATTERN_IMPERSONATION}

ID: ${code.substring(0, 8)}`;

export const generateCloudResultMessage = (status, providers) => `クラウド利用・リスク診断
\`\`\`
ステータス: ${status}
クラウド利用: ${providers.join(',') || 'なし'}
\`\`\``;

export const generateImpersonationResultMessage = ({ status, rank, count, bimi, brandTld, vmc, spf, dmarc }) => `なりすまし診断
\`\`\`
ステータス: ${status}
ランク: ${rank}
VMC発行申請: ${vmc ? 'あり' : 'なし'}
BIMIレコード設定: ${bimi ? 'あり' : 'なし'}
SPFレコード設定: ${spf ? 'あり' : 'なし'}
DMARCレコード設定: ${dmarc ? 'あり' : 'なし'}
ブランドTLD利用: ${brandTld ? 'あり' : 'なし'}
類似ドメイン数量: ${count}
\`\`\``;

export const generateNdsResultMessage = ({ scanId, status, rank, critical, high, medium, low, info, titles }) => `Webサイト脆弱性診断
\`\`\`
scanId: ${scanId}
ステータス: ${status}
ランク: ${rank}
緊急数: ${critical}
高リスク数: ${high}
中リスク数: ${medium}
低リスク数: ${low}
他参考情報数: ${info}

${titles.map(title => `- ${title}`).join('\n')}
\`\`\``;

export const generateSslResultMessage = (status, hostnameVerification, certVerification, freeSslProvider, earliestExpires) => `実在証明・盗聴防止（SSL）診断
\`\`\`
ステータス: ${status}
ドメイン認証: ${status === 'error' ? 'なし' : (hostnameVerification ? 'あり' : 'なし')}
有効性: ${status === 'error' ? 'なし' : (certVerification ? 'あり' : 'なし')}
無料SSL利用: ${freeSslProvider ?? 'なし'}
有効期限: ${earliestExpires ?? 'なし'}
\`\`\``;

export const generatePasswordContactMessage = ({ code, email, fullname, telephone, consultation, contactedAt }) => `
お問い合わせがありました。

${fullname}|${email}|${telephone}|${contactedAt}|${consultation}

ID: ${code.substring(0, 8)}`;

export const generateSiteRiskContactMessage = ({ code, email, fullname, telephone, contactedAt, targets, consultation }) => `
お問い合わせがありました。
${targets.join('・')}

${fullname}|${email}|${telephone}|${contactedAt}|${consultation}

ID: ${code.substring(0, 8)}`;

export const generateWatchHibpMessage = breaches => `*New Breaches Detected (${breaches.length})*

${breaches.map(breach => `
*${breach.Title}*
Domain: ${breach.Domain}`).join('\n\n')}`;

export const generateBrandTldContactMessage = ({ email, fullname, telephone, company, contactedAt, tld }) => `
お問い合わせがありました。

${company}|${fullname}|${email}|${telephone}|${tld}|${contactedAt}`;
