import Firestore from '@google-cloud/firestore';

const firestore = new Firestore({ projectId: process.env.PROJECT_ID });

const getDocRef = ({ collection, docId }) => {
  return firestore.collection(collection).doc(docId);
};

export const runTransaction = async (func) => {
  return firestore.runTransaction(func);
};

export const docExists = async ({ collection, docId }) => {
  const ref = getDocRef({ collection, docId });
  const doc = await ref.get();
  return doc.exists;
};

export const saveDoc = async ({ collection, docId, data }) => {
  const ref = getDocRef({ collection, docId });
  await ref.set(data, { merge: true });
};

export const getDoc = async ({ collection, docId }) => {
  const ref = getDocRef({ collection, docId });
  const doc = await ref.get();
  return doc;
};

export const getDocsByDateRange = async ({ collection, from, to }) => {
  const snapshot = await firestore.collection(collection)
    .where('createdAt', '>=', from)
    .where('createdAt', '<', to)
    .get();

  return snapshot.docs.map(doc => doc.data());
};

export const getDocsByNextCheckedAt = async ({ collection, to }) => {
  const snapshot = await firestore.collection(collection)
    .where('nextCheckedAt', '<', to)
    .get();

  return snapshot.docs;
};

export default firestore;
